{"users": [{"name": "rabbitmq", "password_hash": "pt5JXbtsN4y7Vd8CDWvO/xkKJmxqEDrWkXgi/OgdNGqCqyOR", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "rabbitmq", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "queues": [{"name": "librenms_default", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "device_actions", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "device_discovery", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "import_factory_devices", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}]}