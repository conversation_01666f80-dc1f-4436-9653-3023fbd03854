#!/bin/bash
set -euo pipefail
set -o errexit
set -o errtrace
IFS=$'\n\t'

mkdir -p ${MOUNT_POINT}

export S3_ACL=${S3_ACL:-private}
export AWS_WIREGUARD_PEERS_BUCKET=${AWS_WIREGUARD_PEERS_BUCKET:-$AWS_WIREGUARD_PEERS_BUCKET}
export AWSACCESSKEYID=${AWSACCESSKEYID:-$AWS_ACCESS_KEY_ID}
export AWSSECRETACCESSKEY=${AWSSECRETACCESSKEY:-$AWS_SECRET_ACCESS_KEY}

touch /etc/passwd-s3fs
echo "${AWSACCESSKEYID}:${AWSSECRETACCESSKEY}" > /etc/passwd-s3fs
chmod 600 /etc/passwd-s3fs

s3fs ${AWS_WIREGUARD_PEERS_BUCKET} ${MOUNT_POINT} -o nosuid,nonempty,nodev,allow_other,default_acl=${S3_ACL},retries=5,passwd_file=/etc/passwd-s3fs

if [ -z "$( ls -A ${MOUNT_POINT} )" ]; then
   echo "S3 bucket is empty"
   cp -r /peers/* ${MOUNT_POINT}
fi

#fusermount -u ${MOUNT_POINT}
#s3fs ${AWS_WIREGUARD_PEERS_BUCKET} ${MOUNT_POINT} -o nosuid,nonempty,nodev,allow_other,default_acl=${S3_ACL},retries=5,passwd_file=/etc/passwd-s3fs

exec "./init"
