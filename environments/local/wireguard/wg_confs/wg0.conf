[Interface]
ListenPort = 51820
Address = FDE3:8675:3092:0000:0000:0000:0000:0001/64
PrivateKey = +Gn0EoQ4hUqLXDgU1sMjLIm6WtW+KZiIbY7rP7H3/GM=

PostUp = iptables -A FORWARD -i %i -j ACCEPT; iptables -A FORWARD -o %i -j ACCEPT; iptables -t nat -A POSTROUTING -o eth+ -j MASQUERADE; ip6tables -A FORWARD -i %i -j ACCEPT; ip6tables -A FORWARD -o %i -j ACCEPT
PostDown = iptables -D FORWARD -i %i -j ACCEPT; iptables -D FORWARD -o %i -j ACCEPT; iptables -t nat -D POSTROUTING -o eth+ -j MASQUERADE; ip6tables -D FORWARD -i %i -j ACCEPT; ip6tables -D FORWARD -o %i -j ACCEPT

#add all peers.  NOTE if directory is empty it will try to add a file called *
PostUp = for f in /s3bucket/*; do if [ -f "$f" ]; then wg addconf wg0 $f; fi done

