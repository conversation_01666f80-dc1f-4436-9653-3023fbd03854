name: librenms

services:
  db:
#    image: mariadb:10.5
    build:
      context: ../../
      dockerfile: ./environments/local/mariadb/Dockerfile
    container_name: librenms_db
    command:
      - "mysqld"
      - "--innodb-file-per-table=1"
      - "--lower-case-table-names=0"
      - "--character-set-server=utf8mb4"
      - "--collation-server=utf8mb4_unicode_ci"
    volumes:
      - "../../volumes/database:/var/lib/mysql"
    environment:
      - "TZ=${TZ}"
      - "MYSQL_ALLOW_EMPTY_PASSWORD=yes"
      - "MYSQL_DATABASE=${MYSQL_DATABASE}"
      - "MYSQL_USER=${MYSQL_USER}"
      - "MYSQL_PASSWORD=${MYSQL_PASSWORD}"
    healthcheck:
      test: [ "CMD", "healthcheck.sh", "--connect", "--innodb_initialized" ]
      start_period: 1m
      start_interval: 10s
      interval: 1m
      timeout: 5s
      retries: 3
    networks:
      - arra_web

  redis:
    image: redis:5.0-alpine
    container_name: librenms_redis
#    command: redis-server --appendonly yes --requirepass "${REDIS_PASSWORD}"
#    volumes:
#      - "../../volumes/redis:/data"
    environment:
      - "TZ=${TZ}"
#    ports:
#      - "8002:6379"
    networks:
      - arra_web

#  msmtpd:
#    image: crazymax/msmtpd:latest
#    container_name: librenms_msmtpd
#    env_file:
#      - "./librenms/msmtpd.env"

  librenms:
#    image: librenms/librenms:latest
    build:
      context: ../../
      dockerfile: ./environments/local/librenms/Dockerfile
    container_name: librenms
    hostname: librenms
    cap_add:
      - NET_ADMIN
      - NET_RAW
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
#      msmtpd:
#        condition: service_started
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
    healthcheck:
      test: [ "CMD", "/etc/healthcheck.sh" ]
#      test: ["CMD-SHELL", "docker logs librenms 2>&1 | grep -q 'ready to handle connections'"]
      interval: 30s
      timeout: 30s
      retries: 3
      start_period: 2m
      start_interval: 10s
    networks:
      arra_web:
        ipv4_address: ************
        aliases:
          - "nms.arra.local"
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=arra_web"
      - "traefik.http.routers.nms.rule=Host(`nms.arra.local`)"
      - "traefik.http.routers.nms.entrypoints=websecure"
      - "traefik.http.routers.nms.tls=true"
      - "traefik.http.services.nms.loadbalancer.server.port=8000"

  dispatcher:
#    image: librenms/librenms:latest
    build:
      context: ../../
      dockerfile: ./environments/local/librenms/Dockerfile
    container_name: librenms_dispatcher
    hostname: librenms-dispatcher
    cap_add:
      - NET_ADMIN
      - NET_RAW
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
#    ports:
#      - target: 51820
#        published: 51820
#        protocol: udp
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "DISPATCHER_NODE_ID=dispatcher1"
      - "SIDECAR_DISPATCHER=1"
    networks:
        - arra_web

  syslogng:
#    image: librenms/librenms:latest
    build:
      context: ../../
      dockerfile: ./environments/local/librenms/Dockerfile
    container_name: librenms_syslogng
    hostname: librenms-syslogng
    cap_add:
      - NET_ADMIN
      - NET_RAW
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    ports:
      - target: 514
        published: 514
        protocol: tcp
      - target: 514
        published: 514
        protocol: udp
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "SIDECAR_SYSLOGNG=1"
    networks:
      - arra_web

  snmptrapd:
#    image: librenms/librenms:latest
    build:
      context: ../../
      dockerfile: ./environments/local/librenms/Dockerfile
    container_name: librenms_snmptrapd
    hostname: librenms-snmptrapd
    cap_add:
      - NET_ADMIN
      - NET_RAW
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    ports:
      - target: 162
        published: 162
        protocol: tcp
      - target: 162
        published: 162
        protocol: udp
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "SIDECAR_SNMPTRAPD=1"
    networks:
      - arra_web

  librenms-cron:
    #    image: registry.gitlab.com/arranetworks/web/deploy/nms:development
    build:
      context: ../../
      dockerfile: ./environments/local/librenms/Dockerfile
    container_name: librenms_cron
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "SIDECAR_CRON=1"
    networks:
      - arra_web

  mailhog:
    image: mailhog/mailhog:latest
    container_name: "librenms_mailhog"
    networks:
      - arra_web
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mail-nms.rule=Host(`mail-nms.arra.local`)"
      - "traefik.http.routers.mail-nms.entrypoints=websecure"
      - "traefik.http.routers.mail-nms.tls=true"
      - "traefik.http.services.mail-nms.loadbalancer.server.port=8025"

  phpmyadmin:
    image: phpmyadmin:5.2.0
    container_name: "librenms_pma"
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: "db"
      PMA_USER: "root"
      PMA_PASSWORD: ''
      UPLOAD_LIMIT: '512M'
    depends_on:
      - db
    networks:
      - arra_web
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pma-nms.rule=Host(`pma-nms.arra.local`)"
      - "traefik.http.routers.pma-nms.entrypoints=websecure"
      - "traefik.http.routers.pma-nms.tls=true"
      - "traefik.http.services.pma-nms.loadbalancer.server.port=80"

  rabbitmq:
    container_name: "librenms_rabbitmq"
    image: rabbitmq:3-management-alpine
    volumes:
      - "./rabbitmq/conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro"
      - "./rabbitmq/conf/definitions.json:/etc/rabbitmq/definitions.json:ro"
#    environment:
#      RABBITMQ_DEFAULT_USER: "${RABBITMQ_USER}"
#      RABBITMQ_DEFAULT_PASS: "${RABBITMQ_PASSWORD}"
    ports:
      # AMQP protocol port
      - "5672:5672"
      # HTTP management UI
      - "15672:15672"
    networks:
      - arra_web

  wireguard:
#    image: lscr.io/linuxserver/wireguard:latest
    build:
      context: ../../
      dockerfile: ./environments/local/wireguard/Dockerfile
    container_name: librenms_wireguard
    cap_add:
      - NET_ADMIN
      - SYS_ADMIN
      - SYS_MODULE #optional
    devices:
      - /dev/fuse
    environment:
      - AWSACCESSKEYID=${AWS_ACCESS_KEY_ID}
      - AWSSECRETACCESSKEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_WIREGUARD_PEERS_BUCKET=${AWS_WIREGUARD_PEERS_BUCKET}
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "TZ=${TZ}"
      - SERVERURL=auto #optional
      - SERVERPORT=51820 #optional
      - PEERS=0 #optional
      - PEERDNS=auto #optional
      - INTERNAL_SUBNET= #optional
      - ALLOWEDIPS=0.0.0.0/0 #optional
      - PERSISTENTKEEPALIVE_PEERS= #optional
      - LOG_CONFS=false #optional
    volumes:
      - "./wireguard/server:/config/server:ro"
      - "./wireguard/wg_confs/wg0.conf:/config/wg_confs/wg0.conf:ro"
#     - /lib/modules:/lib/modules #optional
    ports:
      - "51820:51820/udp"
    sysctls:
      - net.ipv4.conf.all.src_valid_mark=1
    networks:
      - arra_web

networks:
  arra_web:
    external: true
