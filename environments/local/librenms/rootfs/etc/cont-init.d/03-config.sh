#!/usr/bin/with-contenv bash
# shellcheck shell=bash
set -e

# From https://github.com/docker-library/mariadb/blob/master/docker-entrypoint.sh#L21-L41
# usage: file_env VAR [DEFAULT]
#    ie: file_env 'XYZ_DB_PASSWORD' 'example'
# (will allow for "$XYZ_DB_PASSWORD_FILE" to fill in the value of
#  "$XYZ_DB_PASSWORD" from a file, especially for <PERSON><PERSON>'s secrets feature)
file_env() {
  local var="$1"
  local fileVar="${var}_FILE"
  local def="${2:-}"
  if [ "${!var:-}" ] && [ "${!fileVar:-}" ]; then
    echo >&2 "error: both $var and $fileVar are set (but are exclusive)"
    exit 1
  fi
  local val="$def"
  if [ "${!var:-}" ]; then
    val="${!var}"
  elif [ "${!fileVar:-}" ]; then
    val="$(<"${!fileVar}")"
  fi
  export "$var"="$val"
  unset "$fileVar"
}

TZ=${TZ:-UTC}

MEMORY_LIMIT=${MEMORY_LIMIT:-256M}
UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-16M}
CLEAR_ENV=${CLEAR_ENV:-yes}
FPM_PM_MAX_CHILDREN=${FPM_PM_MAX_CHILDREN:-15}
FPM_PM_START_SERVERS=${FPM_PM_START_SERVERS:-2}
FPM_PM_MIN_SPARE_SERVERS=${FPM_PM_MIN_SPARE_SERVERS:-1}
FPM_PM_MAX_SPARE_SERVERS=${FPM_PM_MAX_SPARE_SERVERS:-6}
OPCACHE_MEM_SIZE=${OPCACHE_MEM_SIZE:-128}
LISTEN_IPV6=${LISTEN_IPV6:-true}
REAL_IP_FROM=${REAL_IP_FROM:-"0.0.0.0/32"}
REAL_IP_HEADER=${REAL_IP_HEADER:-"X-Forwarded-For"}
LOG_IP_VAR=${LOG_IP_VAR:-remote_addr}
MAX_INPUT_VARS=${MAX_INPUT_VARS:-1000}

MEMCACHED_PORT=${MEMCACHED_PORT:-11211}

DB_PORT=${DB_PORT:-3306}
DB_NAME=${DB_NAME:-librenms}
DB_USER=${DB_USER:-librenms}
DB_TIMEOUT=${DB_TIMEOUT:-30}
DB_CONNECTION=${DB_CONNECTION:-mysql}

APP_ENV=${APP_ENV:-production}
APP_DEBUG=${APP_DEBUG:-false}

LIBRENMS_BASE_URL=${LIBRENMS_BASE_URL:-/}

QUEUE_CONNECTION=${QUEUE_CONNECTION:-sync}
APP_SUPERADMINS=${APP_SUPERADMINS:-""}
CACHE_DRIVER=${CACHE_DRIVER:-"database"}

AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-"********************"}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-"HHkQCT1cVi3H5ehWT2iN/8ow7xBOPoZLw7YJIuW5"}
AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-"us-east-2"}
AWS_WIREGUARD_PEERS_BUCKET=${AWS_WIREGUARD_PEERS_BUCKET:-"wireguard-peers-local"}
AWS_USE_PATH_STYLE_ENDPOINT=${AWS_USE_PATH_STYLE_ENDPOINT:-false}

RADIUSDESK_URL=${RADIUSDESK_URL:-""}
RADIUSDESK_TOKEN=${RADIUSDESK_TOKEN:-""}
RADIUSDESK_CLOUD_ID=${RADIUSDESK_CLOUD_ID:-""}
RADIUSDESK_LANG=${RADIUSDESK_LANG:-""}

APP_LOCALE=${APP_LOCALE:=en}
APP_FALLBACK_LOCALE=${APP_FALLBACK_LOCALE:=en}
APP_FAKER_LOCALE=${APP_FAKER_LOCALE:=en_US}
APP_MAINTENANCE_DRIVER=${APP_MAINTENANCE_DRIVER:=file}
APP_MAINTENANCE_STORE=${APP_MAINTENANCE_STORE:=database}
PHP_CLI_SERVER_WORKERS=${PHP_CLI_SERVER_WORKERS:=4}
BCRYPT_ROUNDS=${BCRYPT_ROUNDS:=12}
LOG_STACK=${LOG_STACK:=single}
SESSION_ENCRYPT=${SESSION_ENCRYPT:=false}
SESSION_PATH=${SESSION_PATH:=/}
SESSION_DOMAIN=${SESSION_DOMAIN:=null}

# Timezone
echo "Setting timezone to ${TZ}..."
ln -snf /usr/share/zoneinfo/${TZ} /etc/localtime
echo ${TZ} >/etc/timezone

# PHP
echo "Setting PHP-FPM configuration..."
sed -e "s/@MEMORY_LIMIT@/$MEMORY_LIMIT/g" \
  -e "s/@UPLOAD_MAX_SIZE@/$UPLOAD_MAX_SIZE/g" \
  -e "s/@CLEAR_ENV@/$CLEAR_ENV/g" \
  -e "s/@FPM_PM_MAX_CHILDREN@/$FPM_PM_MAX_CHILDREN/g" \
  -e "s/@FPM_PM_START_SERVERS@/$FPM_PM_START_SERVERS/g" \
  -e "s/@FPM_PM_MIN_SPARE_SERVERS@/$FPM_PM_MIN_SPARE_SERVERS/g" \
  -e "s/@FPM_PM_MAX_SPARE_SERVERS@/$FPM_PM_MAX_SPARE_SERVERS/g" \
  /tpls/etc/php83/php-fpm.d/www.conf >/etc/php83/php-fpm.d/www.conf

echo "Setting PHP INI configuration..."
sed -i "s|memory_limit.*|memory_limit = ${MEMORY_LIMIT}|g" /etc/php83/php.ini
sed -i "s|;date\.timezone.*|date\.timezone = ${TZ}|g" /etc/php83/php.ini
sed -i "s|;max_input_vars.*|max_input_vars = ${MAX_INPUT_VARS}|g" /etc/php83/php.ini

# OpCache
echo "Setting OpCache configuration..."
sed -e "s/@OPCACHE_MEM_SIZE@/$OPCACHE_MEM_SIZE/g" \
  /tpls/etc/php83/conf.d/opcache.ini >/etc/php83/conf.d/opcache.ini

# Nginx
echo "Setting Nginx configuration..."
sed -e "s#@UPLOAD_MAX_SIZE@#$UPLOAD_MAX_SIZE#g" \
  -e "s#@REAL_IP_FROM@#$REAL_IP_FROM#g" \
  -e "s#@REAL_IP_HEADER@#$REAL_IP_HEADER#g" \
  -e "s#@LOG_IP_VAR@#$LOG_IP_VAR#g" \
  /tpls/etc/nginx/nginx.conf >/etc/nginx/nginx.conf

if [ "$LISTEN_IPV6" != "true" ]; then
  sed -e '/listen \[::\]:/d' -i /etc/nginx/nginx.conf
fi

# SNMP
echo "Updating SNMP community..."
file_env 'LIBRENMS_SNMP_COMMUNITY' 'librenmsdocker'
sed -i -e "s/RANDOMSTRINGGOESHERE/${LIBRENMS_SNMP_COMMUNITY}/" /etc/snmp/snmpd.conf

# Init files and folders
echo "Initializing LibreNMS files / folders..."
mkdir -p /data/config /data/logs /data/monitoring-plugins /data/plugins /data/rrd /data/alert-templates


touch /data/logs/librenms.log
#rm -rf ${LIBRENMS_PATH}/logs
#rm -f ${LIBRENMS_PATH}/config.d/*
mkdir -p /etc/logrotate.d
touch /etc/logrotate.d/librenms

echo "Setting LibreNMS configuration..."

# Env file
if [ -z "$DB_HOST" ]; then
  echo >&2 "ERROR: DB_HOST must be defined"
  exit 1
fi
file_env 'DB_PASSWORD'
if [ -z "$DB_PASSWORD" ]; then
  echo >&2 "ERROR: Either DB_PASSWORD or DB_PASSWORD_FILE must be defined"
  exit 1
fi
if [ ! -f "/data/.env" ]
then
cat >${LIBRENMS_PATH}/.env <<EOL
APP_URL=${LIBRENMS_BASE_URL}
APP_ENV=${APP_ENV}
APP_DEBUG=${APP_DEBUG}
SESSION_SECURE_COOKIE=${SESSION_SECURE_COOKIE}
CACHE_DRIVER=${CACHE_DRIVER}
DB_HOST=${DB_HOST}
DB_PORT=${DB_PORT}
DB_DATABASE=${DB_NAME}
DB_USERNAME=${DB_USER}
DB_PASSWORD="${DB_PASSWORD}"
DB_CONNECTION=${DB_CONNECTION}
QUEUE_CONNECTION="${QUEUE_CONNECTION}"
RABBITMQ_QUEUE="${RABBITMQ_QUEUE_DEFAULT}"
RABBITMQ_HOST="${RABBITMQ_HOST}"
RABBITMQ_PORT="${RABBITMQ_PORT}"
RABBITMQ_USER="${RABBITMQ_USER}"
RABBITMQ_PASSWORD="${RABBITMQ_PASSWORD}"
APP_LOCALE=${APP_LOCALE}
APP_FALLBACK_LOCALE=${APP_FALLBACK_LOCALE}
APP_FAKER_LOCALE=${APP_FAKER_LOCALE}
APP_MAINTENANCE_DRIVER=${APP_MAINTENANCE_DRIVER}
APP_MAINTENANCE_STORE=${APP_MAINTENANCE_STORE}
PHP_CLI_SERVER_WORKERS=${PHP_CLI_SERVER_WORKERS}
BCRYPT_ROUNDS=${BCRYPT_ROUNDS}
LOG_STACK=${LOG_STACK}
SESSION_ENCRYPT=${SESSION_ENCRYPT}
SESSION_PATH=${SESSION_PATH}
SESSION_DOMAIN=${SESSION_DOMAIN}
APP_SUPERADMINS="${APP_SUPERADMINS}"
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
AWS_WIREGUARD_PEERS_BUCKET=${AWS_WIREGUARD_PEERS_BUCKET}
AWS_USE_PATH_STYLE_ENDPOINT=${AWS_USE_PATH_STYLE_ENDPOINT}
RADIUSDESK_URL=${RADIUSDESK_URL}
RADIUSDESK_TOKEN=${RADIUSDESK_TOKEN}
RADIUSDESK_CLOUD_ID=${RADIUSDESK_CLOUD_ID}
RADIUSDESK_LANG=${RADIUSDESK_LANG}
EOL
fi

# Config : Directories
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/directories.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/directories.yaml <<EOL
install_dir: '${LIBRENMS_PATH}'
log_dir: /data/logs
rrd_dir: /data/rrd
EOL
fi
ln -sf /data/logs ${LIBRENMS_PATH}/logs

# Config : Server
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/server.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/server.yaml <<EOL
own_hostname: '$(hostname)'
base_url: '${LIBRENMS_BASE_URL}'
EOL
fi

# Config : User
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/user.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/user.yaml <<EOL
user: librenms
group: librenms
EOL
fi

# Config : Fping
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/fping.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/fping.yaml <<EOL
fping: /usr/sbin/fping
fping6: /usr/sbin/fping6
EOL
fi

# Config : ipmitool
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/ipmitool.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/ipmitool.yaml <<EOL
ipmitool: /usr/sbin/ipmitool
EOL
fi

# Config : Disable autoupdate (set in config.php so it cannot be overridden in the webui)
if [ ! -f "${LIBRENMS_PATH}/config.d/autoupdate.php" ]; then
cat >${LIBRENMS_PATH}/config.d/autoupdate.php <<EOL
<?php
\$config['update'] = 0;
EOL
fi

# Config :Arra Config
if [ ! -f "${LIBRENMS_PATH}/config.d/arra.php" ]; then
cat >${LIBRENMS_PATH}/config.d/arra.php <<EOL
<?php
\$config['page_title_suffix'] = "ArraNMS";
\$config['project_name'] = "ArraNMS";
\$config['webui']['custom_css'] = ["css/arra/arra.css"];
\$config['permission']['device_group']['allow_dynamic'] = true;
EOL
fi

# Config :ArraOS Config
if [ ! -f "${LIBRENMS_PATH}/config.d/arraos.php" ]; then
cat >${LIBRENMS_PATH}/config.d/arraos.php <<EOL
<?php
\$config['os']['arra']['bad_if'][] = 'lo';
\$config['os']['arra']['bad_if'][] = 'ip6tnl0';
\$config['os']['arra']['bad_if'][] = 'br-lan';
\$config['os']['arra']['bad_if'][] = 'anygw';
\$config['os']['arra']['bad_if'][] = 'arra';
\$config['os']['arra']['bad_if'][] = 'arradev';
\$config['os']['arra']['bad_if'][] = 'ifb0';
\$config['os']['arra']['bad_if'][] = 'ifb1';
\$config['os']['arra']['bad_if'][] = 'teql0';
\$config['os']['arra']['bad_ifname_regexp'][] = '/^X7.*/';
\$config['os']['arra']['bad_ifname_regexp'][] = '/^ztqu.*/';
\$config['os']['arra']['bad_ifname_regexp'][] = '/^wlan[0-9]-mesh_[0-9]/';
\
\$config['os']['arra']['good_if'][] = 'Qualcomm Atheros QCA986x/988x 802.11ac Wireless Network Adapter';
\$config['os']['arra']['good_ifname_regexp'][] = '/^eth[0-9]/';
\$config['os']['arra']['good_ifname_regexp'][] = '/^wlan[0-9]-mesh/';
\$config['os']['arra']['good_ifname_regexp'][] = '/^group-ap[0-9]/';
EOL
fi

# Config : Services
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/services.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/services.yaml <<EOL
show_services: true
nagios_plugins: /usr/lib/monitoring-plugins
EOL
fi

# Config : RRDCached, apply RRDCACHED_SERVER as php as it would be expected to change with the variable
if [ -n "${RRDCACHED_SERVER}" ]; then
  cat >${LIBRENMS_PATH}/config.d/rrdcached.php <<EOL
<?php
\$config['rrdcached'] = "${RRDCACHED_SERVER}";
EOL
fi
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/rrdtool.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/rrdtool.yaml <<EOL
rrdtool_version: "1.7.2"
EOL
fi

# Config : Dispatcher
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/dispatcher.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/dispatcher.yaml <<EOL
service_update_enabled: false
service_watchdog_enabled: false
EOL
fi

# Config : SMTP
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/smtp.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/smtp.yaml <<EOL
email_html: true
email_backend: "smtp"
email_smtp_auth: false
email_smtp_port: 1025
email_from: "<EMAIL>"
email_smtp_host: "mailhog"
EOL
fi

# Config : WEB UI
if [ ! -f "${LIBRENMS_PATH}/database/seeders/config/webui.yaml" ]; then
cat >${LIBRENMS_PATH}/database/seeders/config/webui.yaml <<EOL
uptime_warning: 0
favicon: "images/arra/favicon.ico"
title_image: "images/arra/logo_light.svg"
EOL
fi

# Check plugins
echo "Checking LibreNMS plugins..."
plugins=$(ls -l /data/plugins | egrep '^d' | awk '{print $9}')
for plugin in ${plugins}; do
  echo "  Linking plugin ${plugin}..."
  if [ -d "${LIBRENMS_PATH}/html/plugins/${plugin}" ]; then
    rm -rf "${LIBRENMS_PATH}/html/plugins/${plugin}"
  fi
  ln -sf "/data/plugins/${plugin}" "${LIBRENMS_PATH}/html/plugins/${plugin}"
  chown -h librenms:librenms "${LIBRENMS_PATH}/html/plugins/${plugin}"
done

# Fix perms
echo "Fixing perms..."
chown librenms:librenms /data/config /data/monitoring-plugins /data/plugins /data/rrd /data/alert-templates
chown -R librenms:librenms /data/logs ${LIBRENMS_PATH}/config.d ${LIBRENMS_PATH}/bootstrap ${LIBRENMS_PATH}/logs ${LIBRENMS_PATH}/storage
chmod ug+rw /data/logs /data/rrd ${LIBRENMS_PATH}/bootstrap/cache ${LIBRENMS_PATH}/storage ${LIBRENMS_PATH}/storage/framework/*

# Check additional Monitoring plugins
echo "Checking additional Monitoring plugins..."
mon_plugins=$(ls -l /data/monitoring-plugins | egrep '^-' | awk '{print $9}')
for mon_plugin in ${mon_plugins}; do
  if [ -f "/usr/lib/monitoring-plugins/${mon_plugin}" ]; then
    echo "  WARNING: Official Monitoring plugin ${mon_plugin} cannot be overriden. Skipping..."
    continue
  fi
  if [[ ${mon_plugin} != check_* ]]; then
    echo "  WARNING: Monitoring plugin filename ${mon_plugin} invalid. It must start with 'check_'. Skipping..."
    continue
  fi
  if [[ ! -x "/data/monitoring-plugins/${mon_plugin}" ]]; then
    echo "  WARNING: Monitoring plugin file ${mon_plugin} has to be executable. Skipping..."
    continue
  fi
  echo "  Adding ${mon_plugin} Monitoring plugin"
  ln -sf /data/monitoring-plugins/${mon_plugin} /usr/lib/monitoring-plugins/${mon_plugin}
done

# Check alert templates
echo "Checking alert templates..."
templates=$(ls -l /data/alert-templates | egrep '^-' | awk '{print $9}')
for template in ${templates}; do
  if [ -f "${LIBRENMS_PATH}/resources/views/alerts/templates/${template}" ]; then
    echo "  WARNING: Default alert template ${template} cannot be overriden. Skipping..."
    continue
  fi
  if [[ ${template} != *.php ]]; then
    echo "  WARNING: Alert template filename ${template} invalid. It must end with '.php'. Skipping..."
    continue
  fi
  echo "  Adding ${template} alert template"
  ln -sf /data/alert-templates/${template} ${LIBRENMS_PATH}/resources/views/alerts/templates/${template}
done
