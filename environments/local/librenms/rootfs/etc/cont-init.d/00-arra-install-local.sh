#!/usr/bin/with-contenv sh
# shellcheck shell=sh
set -e

# From https://github.com/docker-library/mariadb/blob/master/docker-entrypoint.sh#L21-L41
# usage: file_env VAR [DEFAULT]
#    ie: file_env 'XYZ_DB_PASSWORD' 'example'
# (will allow for "$XYZ_DB_PASSWORD_FILE" to fill in the value of
#  "$XYZ_DB_PASSWORD" from a file, especially for <PERSON><PERSON>'s secrets feature)
file_env() {
  local var="$1"
  local fileVar="${var}_FILE"
  local def="${2:-}"
  if [ "${!var:-}" ] && [ "${!fileVar:-}" ]; then
    echo >&2 "error: both $var and $fileVar are set (but are exclusive)"
    exit 1
  fi
  local val="$def"
  if [ "${!var:-}" ]; then
    val="${!var}"
  elif [ "${!fileVar:-}" ]; then
    val="$(<"${!fileVar}")"
  fi
  export "$var"="$val"
  unset "$fileVar"
}

SIDECAR_DISPATCHER=${SIDECAR_DISPATCHER:-0}
SIDECAR_SYSLOGNG=${SIDECAR_SYSLOGNG:-0}
SIDECAR_SNMPTRAPD=${SIDECAR_SNMPTRAPD:-0}
SIDECAR_CRON=${SIDECAR_CRON:-0}
SIDECAR_QUEUE=${SIDECAR_QUEUE:-0}

if [ ! -f "/tmp/librenms_ready" ]
then
    rm -f /tmp/librenms_ready
fi

if [ "$SIDECAR_DISPATCHER" = "1" ] || [ "$SIDECAR_SYSLOGNG" = "1" ] || [ "$SIDECAR_SNMPTRAPD" = "1" ] || [ "$SIDECAR_CRON" = "1" ] || [ "$SIDECAR_QUEUE" = "1" ]; then
  exit 0
fi

mkdir -p /opt/librenms/vendor/laravel-notification-channels/webpush

echo
echo "Install composer packages ..."
COMPOSER_CACHE_DIR="/tmp" composer install --no-interaction --no-ansi
git config --global --add safe.directory /opt/librenms

echo
echo "Set config files ..."

mkdir -p config.d
mkdir -p logs

if [ ! -f "config.php" ]
then
    cp config.php.default config.php
    echo "foreach (glob(\"/data/config/*.php\") as \$filename) include \$filename;" >> config.php
    echo "foreach (glob(\"${LIBRENMS_PATH}/config.d/*.php\") as \$filename) include \$filename;" >> config.php
fi

sed -i '/runningUser/d' lnms

echo
echo Remove unused files ...
rm -rf html/plugins/Test

echo "Done!"
