#!/usr/bin/with-contenv bash
# shellcheck shell=bash
set -e

CRONTAB_PATH="/var/spool/cron/crontabs"

LIBRENMS_DAILY_SCHEDULE="15 0 * * *"

SIDECAR_CRON=${SIDECAR_CRON:-0}

# Continue only if sidecar cron container
if [ "$SIDECAR_CRON" != "1" ]; then
  exit 0
fi

# Init
rm -rf ${CRONTAB_PATH}
mkdir -m 0644 -p ${CRONTAB_PATH}
touch ${CRONTAB_PATH}/librenms

# Cron
echo "Creating LibreNMS daily.sh cron task with the following period fields: $LIBRENMS_DAILY_SCHEDULE"
echo "${LIBRENMS_DAILY_SCHEDULE} cd /opt/librenms/ && bash daily.sh" >>${CRONTAB_PATH}/librenms

echo "Creating LibreNMS cron artisan schedule:run"
echo "* * * * * php /opt/librenms/artisan schedule:run --no-ansi --no-interaction > /dev/null 2>&1" >>${CRONTAB_PATH}/librenms

# Fix perms
echo "Fixing crontabs permissions..."
chmod -R 0644 ${CRONTAB_PATH}

# Create service
mkdir -p /etc/services.d/cron
cat >/etc/services.d/cron/run <<EOL
#!/usr/bin/execlineb -P
with-contenv
exec busybox crond -f -L /dev/stdout
EOL
chmod +x /etc/services.d/cron/run
