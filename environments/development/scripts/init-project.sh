mkdir -p "volumes/ssl"
mkdir -p "volumes/database"
mkdir -p "volumes/librenms"
mkdir -p "volumes/redis"
mkdir -p "volumes/wireguard"
mkdir -p "volumes/rrd/db"
mkdir -p "volumes/rrd/journal"

source ./.env

if [ ! -f "volumes/ssl/acme.json" ]
then
  touch "volumes/ssl/acme.json"
  chmod 600 "volumes/ssl/acme.json"
fi

docker network create librenmsnw || true

if [ -d "src" ]
then
    echo "Source code already exists."
else
    echo "Cloning NMS Project ..."
    <NAME_EMAIL>:arranetworks/web/apps/nms-app/nms-website.git src
    cd src
    git checkout development
    echo "Waiting for 10 seconds ..."
    sleep 10
    cd ..
fi

cd "${PATH_ENV}" || exit

echo
echo "Start all containers..."
docker compose -f docker-compose.yml up -d

echo
echo "Wait for 20 seconds..."
sleep 20

docker compose exec librenms php lnms arra-billing-plugin:install
docker compose exec librenms php lnms arra-group-settings-plugin:install
docker compose exec librenms php lnms arra-mesh-planner-plugin:install
docker compose exec librenms php scripts/composer_wrapper.php --version
