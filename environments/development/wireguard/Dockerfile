FROM lscr.io/linuxserver/wireguard:latest
LABEL authors="ArraNetworks"

# the following ENV need to be present
ENV IAM_ROLE=none
ENV MOUNT_POINT=/s3bucket
VOLUME /s3bucket

ARG S3FS_VERSION=v1.95

RUN apk --update --no-cache add fuse alpine-sdk automake autoconf libxml2-dev fuse-dev curl-dev git bash supervisor openssh;
RUN git clone https://github.com/s3fs-fuse/s3fs-fuse.git; \
 cd s3fs-fuse; \
 git checkout tags/${S3FS_VERSION}; \
 ./autogen.sh; \
 ./configure --prefix=/usr; \
 make; \
 make install; \
 rm -rf /var/cache/apk/*;

RUN mkdir -p ${MOUNT_POINT}
RUN mkdir -p /arra-scripts

COPY environments/development/wireguard/scripts/docker-entrypoint.sh /docker-entrypoint.sh
COPY environments/development/wireguard/scripts/new-peers-listener.sh /arra-scripts/new-peers-listener.sh
COPY environments/development/wireguard/peers /peers
COPY environments/development/wireguard/server /config/server
COPY environments/development/wireguard/wg_confs/wg0.conf /config/wg_confs/wg0.conf

RUN chmod +x /docker-entrypoint.sh
RUN chmod +x /arra-scripts/new-peers-listener.sh

# Supervisor
RUN mkdir -p /etc/supervisor/conf.d
COPY environments/development/wireguard/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY environments/development/wireguard/scripts/supervisord-run /etc/services.d/supervisord/run
RUN chmod +x /etc/services.d/supervisord/run

ENTRYPOINT ["/docker-entrypoint.sh"]
