services:
  traefik:
    image: traefik:2.5
    container_name: traefik
    command:
      - "--global.checknewversion=false"
      - "--global.sendanonymoususage=false"
      - "--log=true"
      - "--log.level=INFO"
      - "--entrypoints.http=true"
      - "--entrypoints.http.address=:80"
      - "--entrypoints.http.http.redirections.entrypoint.to=https"
      - "--entrypoints.http.http.redirections.entrypoint.scheme=https"
      - "--entrypoints.https=true"
      - "--entrypoints.https.address=:443"
      - "--certificatesresolvers.letsencrypt"
      - "--certificatesresolvers.letsencrypt.acme.storage=acme.json"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=http"
      - "--providers.docker"
      - "--providers.docker.watch=true"
      - "--providers.docker.exposedbydefault=false"
    ports:
      - target: 80
        published: 80
        protocol: tcp
      - target: 443
        published: 443
        protocol: tcp
    volumes:
      - "../../volumes/ssl/acme.json:/acme.json"
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - librenmsnw
    restart: always

  db:
    image: mariadb:10.5
    container_name: librenms_db
    command:
      - "mysqld"
      - "--innodb-file-per-table=1"
      - "--lower-case-table-names=0"
      - "--character-set-server=utf8mb4"
      - "--collation-server=utf8mb4_unicode_ci"
    volumes:
      - "../../volumes/database:/var/lib/mysql"
    environment:
      - "TZ=${TZ}"
      - "MYSQL_ALLOW_EMPTY_PASSWORD=no"
      - "MYSQL_DATABASE=${MYSQL_DATABASE}"
      - "MYSQL_USER=${MYSQL_USER}"
      - "MYSQL_PASSWORD=${MYSQL_PASSWORD}"
      - "MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}"
    healthcheck:
      test: [ "CMD", "healthcheck.sh", "--connect", "--innodb_initialized" ]
      interval: 1m
      timeout: 5s
      retries: 3
    networks:
      - librenmsnw
    restart: always

  redis:
    image: redis:5.0-alpine
    container_name: librenms_redis
    environment:
      - "TZ=${TZ}"
#    ports:
#      - "8002:6379"
    networks:
      - librenmsnw
    restart: always

#  msmtpd:
#    image: crazymax/msmtpd:latest
#    container_name: librenms_msmtpd
#    env_file:
#      - "./librenms/msmtpd.env"
#    restart: always

  librenms:
#    image: registry.gitlab.com/arranetworks/web/deploy/nms:development
    build:
      context: ../../
      dockerfile: ./environments/development/librenms/Dockerfile
    container_name: librenms
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
#       msmtpd:
#         condition: service_started
      traefik:
        condition: service_started
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.librenms.entrypoints=https"
      - "traefik.http.routers.librenms.rule=Host(`${APP_DOMAIN_NAME}`)"
      - "traefik.http.routers.librenms.tls=true"
      - "traefik.http.routers.librenms.tls.certresolver=letsencrypt"
      - "traefik.http.routers.librenms.tls.domains[0].main=${APP_DOMAIN_NAME}"
      - "traefik.http.services.librenms.loadbalancer.server.port=8000"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
    network_mode: service:wireguard
    restart: always
    healthcheck:
      test: [ "CMD", "/etc/healthcheck.sh" ]
      interval: 30s
      timeout: 30s
      retries: 3
      start_period: 2m

  dispatcher:
#    image: registry.gitlab.com/arranetworks/web/deploy/nms:development
    build:
      context: ../../
      dockerfile: ./environments/development/librenms/Dockerfile
    container_name: librenms_dispatcher
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "DISPATCHER_NODE_ID=dispatcher1"
      - "SIDECAR_DISPATCHER=1"
      - "POLLER_NAME=poller1"
    network_mode: service:wireguard
    restart: always

  syslogng:
#    image: registry.gitlab.com/arranetworks/web/deploy/nms:development
    build:
      context: ../../
      dockerfile: ./environments/development/librenms/Dockerfile
    container_name: librenms_syslogng
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    ports:
      - target: 514
        published: 514
        protocol: tcp
      - target: 514
        published: 514
        protocol: udp
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "SIDECAR_SYSLOGNG=1"
    networks:
      - librenmsnw
    restart: always

  snmptrapd:
#    image: registry.gitlab.com/arranetworks/web/deploy/nms:development
    build:
      context: ../../
      dockerfile: ./environments/development/librenms/Dockerfile
    container_name: librenms_snmptrapd
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    ports:
      - target: 162
        published: 162
        protocol: tcp
      - target: 162
        published: 162
        protocol: udp
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "SIDECAR_SNMPTRAPD=1"
    networks:
      - librenmsnw
    restart: always

  librenms-cron:
#    image: registry.gitlab.com/arranetworks/web/deploy/nms:development
    build:
      context: ../../
      dockerfile: ./environments/development/librenms/Dockerfile
    container_name: librenms_cron
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "SIDECAR_CRON=1"
    network_mode: service:wireguard
    restart: always

  librenms-queue:
#    image: registry.gitlab.com/arranetworks/web/deploy/nms:development
    build:
      context: ../../
      dockerfile: ./environments/development/librenms/Dockerfile
    container_name: librenms_queue
    depends_on:
      librenms:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - "../../volumes/librenms:/data"
      - "../../src:/opt/librenms"
    env_file:
      - "./librenms/librenms.env"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "DB_HOST=db"
      - "DB_NAME=${MYSQL_DATABASE}"
      - "DB_USER=${MYSQL_USER}"
      - "DB_PASSWORD=${MYSQL_PASSWORD}"
      - "DB_TIMEOUT=60"
      - "SIDECAR_QUEUE=1"
    network_mode: service:wireguard
    restart: always

  mailhog:
    image: mailhog/mailhog:latest
    container_name: "librenms_mailhog"
    ports:
      - 1025:1025
      - 8025:8025
    networks:
      - librenmsnw
    restart: always

  phpmyadmin:
    image: phpmyadmin:5.2.0
    container_name: "librenms_pma"
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: "db"
      PMA_USER: ${MYSQL_USER}
      PMA_PASSWORD: ${MYSQL_PASSWORD}
      UPLOAD_LIMIT: '512M'
    depends_on:
      - db
    ports:
      - "8081:80"
    networks:
      - librenmsnw
    restart: always

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: "librenms_rabbitmq"
    volumes:
      - "./rabbitmq/conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro"
      - "./rabbitmq/conf/definitions.json:/etc/rabbitmq/definitions.json:ro"
#    environment:
#      RABBITMQ_DEFAULT_USER: "${RABBITMQ_USER}"
#      RABBITMQ_DEFAULT_PASS: "${RABBITMQ_PASSWORD}"
    networks:
      - librenmsnw
    ports:
      # AMQP protocol port
      - "5672:5672"
      # HTTP management UI
      - "15672:15672"

  wireguard:
#    image: registry.gitlab.com/arranetworks/web/deploy/nms/wireguard:development
    build:
      context: ../../
      dockerfile: ./environments/development/wireguard/Dockerfile
    container_name: wireguard
    privileged: true
    cap_add:
      - NET_ADMIN
      - SYS_ADMIN
      - SYS_MODULE #optional
    devices:
      - /dev/fuse
    environment:
      - AWSACCESSKEYID=${AWS_ACCESS_KEY_ID}
      - AWSSECRETACCESSKEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_WIREGUARD_PEERS_BUCKET=${AWS_WIREGUARD_PEERS_BUCKET}
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "TZ=${TZ}"
      - SERVERURL=auto #optional
      - SERVERPORT=51820 #optional
      - PEERS=0 #optional
      - PEERDNS=auto #optional
      - INTERNAL_SUBNET= #optional
      - ALLOWEDIPS=0.0.0.0/0, ::/0 #optional
      - PERSISTENTKEEPALIVE_PEERS=all #optional
      - LOG_CONFS=true #optional
    volumes:
      - "./wireguard/server:/config/server:ro"
      - "./wireguard/wg_confs/wg0.conf:/config/wg_confs/wg0.conf:ro"
#      - "/lib/modules:/lib/modules" #optional
    ports:
      - target: 51820
        published: 51820
        protocol: udp
    sysctls:
      - net.ipv4.ip_forward=1
      - net.ipv4.conf.all.src_valid_mark=1
      - net.ipv6.conf.all.disable_ipv6=0
      - net.ipv6.conf.all.forwarding=1
    networks:
      - librenmsnw
    restart: always

  rrdcached:
    image: crazymax/rrdcached
    container_name: librenms_rrdcached
    ports:
      - target: 42217
        published: 42217
        protocol: tcp
    volumes:
      - "../../volumes/rrd/db:/data/db"
      - "../../volumes/rrd/journal:/data/journal"
    environment:
      - "TZ=${TZ}"
      - "PUID=${PUID}"
      - "PGID=${PGID}"
      - "LOG_LEVEL=LOG_INFO"
      - "WRITE_TIMEOUT=1800"
      - "WRITE_JITTER=1800"
      - "WRITE_THREADS=4"
      - "FLUSH_DEAD_DATA_INTERVAL=3600"
    restart: always
    networks:
      - librenmsnw

networks:
  librenmsnw:
    driver: bridge
    external: true
    enable_ipv6: true
    driver_opts:
      com.docker.network.endpoint.sysctls: "net.ipv6.conf.eth0.proxy_ndp=1"
