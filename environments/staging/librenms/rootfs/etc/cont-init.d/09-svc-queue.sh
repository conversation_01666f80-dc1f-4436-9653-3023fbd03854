#!/usr/bin/with-contenv bash
# shellcheck shell=bash
set -e

SIDECAR_QUEUE=${SIDECAR_QUEUE:-0}

# Continue only if sidecar cron container
if [ "$SIDECAR_QUEUE" != "1" ]; then
  exit 0
fi

echo "Creating LibreNMS Queue Worker service..."

mkdir -p /etc/services.d/queue-work
cat >/etc/services.d/queue-work/run <<EOL
#!/usr/bin/execlineb -P
with-contenv
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/laravel-worker.conf
EOL
chmod +x /etc/services.d/queue-work/run
