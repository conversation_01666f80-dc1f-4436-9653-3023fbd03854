@version:4.8

options {
    chain_hostnames(off);
    flush_lines(0);
    use_dns(no);
    use_fqdn(no);
    owner("root");
    group("adm");
    perm(0640);
    stats(freq(0));
    bad_hostname("^gconfd$");
};

source s_sys {
    system();
    internal();
};

source s_net {
    network(
        transport("tcp")
        port(514)
        max-connections(300)
        log_iw_size(30000)   # Adjusted log_iw_size according to min_log_fifo_size
    );
    network(
        transport("udp")
        port(514)
    );
    unix-stream("/run/syslog-ng/syslog-ng.sock");
};

destination d_librenms {
    program("/opt/librenms/syslog.php" template ("$HOST||$FACILITY||$PRIORITY||$LEVEL||$TAG||$R_YEAR-$R_MONTH-$R_DAY $R_HOUR:$R_MIN:$R_SEC||$MSG||$PROGRAM\n") template-escape(yes));
};

filter f_kernel     { facility(kern); };
filter f_default    { level(info..emerg) and not (facility(mail) or facility(authpriv) or facility(cron)); };
filter f_auth       { facility(authpriv); };
filter f_mail       { facility(mail); };
filter f_emergency  { level(emerg); };
filter f_news       { facility(uucp) or (facility(news) and level(crit..emerg)); };
filter f_boot       { facility(local7); };
filter f_cron       { facility(cron); };

log {
    source(s_net);
    source(s_sys);
    destination(d_librenms);
};

@include "/data/syslog-ng/*.conf"
