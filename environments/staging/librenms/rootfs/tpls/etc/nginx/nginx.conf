pid /var/run/nginx/nginx.pid;
worker_processes auto;
error_log /proc/self/fd/2 info;
worker_rlimit_nofile 8192;

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    include mime.types;
    default_type application/octet-stream;

    aio threads;
    sendfile on;
    large_client_header_buffers 4 16k;

    ## Temp folders
    client_body_temp_path /tmp/nginx 1 2;
    proxy_temp_path /tmp/nginx-proxy;
    fastcgi_temp_path /tmp/nginx-fastcgi;
    uwsgi_temp_path /tmp/nginx-uwsgi;
    scgi_temp_path /tmp/nginx-scgi;

    ## Timeouts
    client_body_timeout   60;
    client_header_timeout 60;
    keepalive_timeout     10 10;
    send_timeout          60;

    ## TCP options
    tcp_nopush  on;
    tcp_nodelay on;

    ## Handling of IPs in proxied and load balancing situations
    set_real_ip_from @REAL_IP_FROM@;
    real_ip_header @REAL_IP_HEADER@;

    # Log
    log_format main '$@LOG_IP_VAR@ - $remote_user [$time_local] '
                    '"$request" $status $body_bytes_sent '
                    '"$http_referer" "$http_user_agent"';
    access_log /proc/self/fd/1 main;

    ## Hide the Nginx version number
    server_tokens off;

    ## Body size
    client_max_body_size @UPLOAD_MAX_SIZE@;
    client_body_buffer_size 128k;

    ## Compression
    gzip              on;
    gzip_buffers      16 8k;
    gzip_comp_level   1;
    gzip_http_version 1.1;
    gzip_min_length   10;
    gzip_types        text/css application/javascript text/javascript application/x-javascript image/svg+xml text/plain text/xsd text/xsl text/xml image/x-icon;
    gzip_vary         on;
    gzip_proxied      any;
    gzip_disable      "msie6";

    ## Serve already compressed files directly, bypassing on-the-fly compression
    gzip_static on;

    server {
        listen 8000;
        listen [::]:8000;

        root /opt/librenms/html;
        index index.php;

        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        location /api/v0 {
            try_files $uri $uri/ /api_v0.php?$query_string;
        }

        location ~ \.php$ {
            fastcgi_split_path_info ^(.+\.php)(/.*)$;
            set $path_info $fastcgi_path_info;
            try_files $fastcgi_script_name =404;
            include fastcgi_params;
            fastcgi_param SERVER_SOFTWARE "";
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $path_info;
            fastcgi_index index.php;
            fastcgi_pass unix:/var/run/php-fpm/php-fpm.sock;
            fastcgi_buffers 256 4k;
            fastcgi_buffer_size 32k;
            fastcgi_intercept_errors on;
            fastcgi_read_timeout 14400;
        }

        location ~ /\.ht {
            deny all;
        }
    }
}
