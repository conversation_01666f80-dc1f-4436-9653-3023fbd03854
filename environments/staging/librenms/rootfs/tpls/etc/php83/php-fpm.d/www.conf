[global]
pid = /var/run/php-fpm/php-fpm.pid
daemonize = no
error_log = /proc/self/fd/2

[www]
listen = /var/run/php-fpm/php-fpm.sock
access.log = /dev/null

pm = dynamic
pm.max_children = @FPM_PM_MAX_CHILDREN@
pm.start_servers = @FPM_PM_START_SERVERS@
pm.min_spare_servers = @FPM_PM_MIN_SPARE_SERVERS@
pm.max_spare_servers = @FPM_PM_MAX_SPARE_SERVERS@
request_terminate_timeout = 0
clear_env = @CLEAR_ENV@

php_admin_value[post_max_size] = @UPLOAD_MAX_SIZE@
php_admin_value[upload_max_filesize] = @UPLOAD_MAX_SIZE@
php_admin_value[max_execution_time] = 10800
php_admin_value[max_input_time] = 3600
php_admin_value[expose_php] = Off
php_admin_value[memory_limit] = @MEMORY_LIMIT@
