APP_URL="https://nms.staging.arranetworks.com"
LIBRENMS_USER=librenms
LIBRENMS_GROUP=librenms
APP_DEBUG=false
APP_ENV=staging
SESSION_SECURE_COOKIE=true
MEMORY_LIMIT=256M
MAX_INPUT_VARS=1000
UPLOAD_MAX_SIZE=16M
OPCACHE_MEM_SIZE=128
REAL_IP_FROM=0.0.0.0/32
REAL_IP_HEADER=X-Forwarded-For
LOG_IP_VAR=remote_addr

CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_CONNECTION=session
SESSION_STORE=redis
REDIS_HOST=redis
DB_CONNECTION=mysql

LIBRENMS_BASE_URL="https://nms.staging.arranetworks.com"
LIBRENMS_SNMP_COMMUNITY=public


QUEUE_CONNECTION=rabbitmq

RABBITMQ_QUEUE_DEFAULT="librenms_default"
RABBITMQ_HOST="rabbitmq"
RABBITMQ_PORT="5672"
RABBITMQ_USER="rabbitmq"
RABBITMQ_PASSWORD="j8Keb778aRZh"

APP_SUPERADMINS=admin

FILESYSTEM_DISK=s3

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=HHkQCT1cVi3H5ehWT2iN/8ow7xBOPoZLw7YJIuW5
AWS_DEFAULT_REGION=us-east-2
AWS_WIREGUARD_PEERS_BUCKET=wireguard-peers-staging
AWS_BUCKET=arra-nms-staging
AWS_BUCKET_PRIVATE=arra-nms-staging-priv
AWS_USE_PATH_STYLE_ENDPOINT=false

RRDCACHED_SERVER=rrdcached:42217

RADIUSDESK_URL=https://radius.staging.arranetworks.com
RADIUSDESK_TOKEN=b4c6ac81-8c7c-4802-b50a-0a6380555b50
RADIUSDESK_CLOUD_ID=23
RADIUSDESK_LANG=4_4
