mkdir -p "volumes/ssl"
mkdir -p "volumes/database"
mkdir -p "volumes/librenms"
mkdir -p "volumes/redis"
mkdir -p "volumes/wireguard"
mkdir -p "volumes/rrd/db"
mkdir -p "volumes/rrd/journal"

source ./.env

if [ ! -f "volumes/ssl/acme.json" ]
then
  touch "volumes/ssl/acme.json"
  chmod 600 "volumes/ssl/acme.json"
fi

docker network create librenmsnw || true

cd "${PATH_ENV}" || exit

echo
echo "Start all containers..."
docker compose -f docker-compose.yml up -d

echo
echo "Wait for 10 seconds..."
sleep 10
