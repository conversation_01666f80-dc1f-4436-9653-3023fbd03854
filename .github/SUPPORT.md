# Support [![](https://isitmaintained.com/badge/resolution/librenms/docker.svg)](https://isitmaintained.com/project/librenms/docker)

## Reporting an issue

Please do a search in [open issues](https://github.com/librenms/docker/issues?utf8=%E2%9C%93&q=) to see if the issue or feature request has already been filed.

If you find your issue already exists, make relevant comments and add your [reaction](https://github.com/blog/2119-add-reactions-to-pull-requests-issues-and-comments). Use a reaction in place of a "+1" comment.

:+1: - upvote

:-1: - downvote

If you cannot find an existing issue that describes your bug or feature, submit an issue using the guidelines below.

## Writing good bug reports and feature requests

File a single issue per problem and feature request.

* Do not enumerate multiple bugs or feature requests in the same issue.
* Do not add your issue as a comment to an existing issue unless it's for the identical input. Many issues look similar, but have different causes.

The more information you can provide, the more likely someone will be successful reproducing the issue and finding a fix.

You are now ready to [create a new issue](https://github.com/librenms/docker/issues/new/choose)!

## Closure policy

* Issues that don't have the information requested above (when applicable) will be closed immediately and the poster directed to the support guidelines.
* Issues that go a week without a response from original poster are subject to closure at my discretion.
