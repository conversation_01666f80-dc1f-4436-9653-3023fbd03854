name: test

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - 'master'
    paths-ignore:
      - '**.md'
  pull_request:
    paths-ignore:
      - '**.md'

env:
  BUILD_TAG: librenms:test
  CONTAINER_NAME: librenms

jobs:
  test:
    runs-on: ubuntu-latest
    continue-on-error: ${{ matrix.version == 'master' }}
    strategy:
      fail-fast: false
      matrix:
        version:
          - ''
          - master
    steps:
      -
        name: Prepare
        if: matrix.version != ''
        run: |
          echo "LIBRENMS_VERSION=${{ matrix.version }}" >> $GITHUB_ENV
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Build
        uses: docker/bake-action@v4
        with:
          targets: image-local
        env:
          DEFAULT_TAG: ${{ env.BUILD_TAG }}
      -
        name: Start
        run: |
          docker compose up -d
        working-directory: test
        env:
          LIBRENMS_IMAGE: ${{ env.BUILD_TAG }}
          LIBRENMS_CONTAINER: ${{ env.CONTAINER_NAME }}
      -
        name: Check container logs
        uses: crazy-max/.github/.github/actions/container-logs-check@main
        with:
          container_name: ${{ env.CONTAINER_NAME }}
          log_check: "ready to handle connections"
          timeout: 120
      -
        name: Logs
        if: always()
        run: |
          docker compose logs
        working-directory: test
        env:
          LIBRENMS_IMAGE: ${{ env.BUILD_TAG }}
          LIBRENMS_CONTAINER: ${{ env.CONTAINER_NAME }}
