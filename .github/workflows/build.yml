name: build

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - 'master'
    tags:
      - '*'
    paths-ignore:
      - '**.md'
  pull_request:
    paths-ignore:
      - '**.md'

env:
  DOCKERHUB_SLUG: librenms/librenms

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.DOCKERHUB_SLUG }}
          tags: |
            type=match,pattern=(.*)-r,group=1
            type=ref,event=pr
            type=edge
          labels: |
            org.opencontainers.image.title=LibreNMS
            org.opencontainers.image.description=Fully featured network monitoring system
            org.opencontainers.image.vendor=LibreNMS
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Login to DockerHub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      -
        name: Build
        uses: docker/bake-action@v4
        with:
          files: |
            ./docker-bake.hcl
            ${{ steps.meta.outputs.bake-file }}
          targets: image-all
          push: ${{ github.event_name != 'pull_request' }}
      -
        name: Check manifest
        if: github.event_name != 'pull_request'
        run: |
          docker buildx imagetools inspect ${{ env.DOCKERHUB_SLUG }}:${{ steps.meta.outputs.version }}
      -
        name: Inspect
        if: github.event_name != 'pull_request'
        run: |
          docker pull ${{ env.DOCKERHUB_SLUG }}:${{ steps.meta.outputs.version }}
          docker image inspect ${{ env.DOCKERHUB_SLUG }}:${{ steps.meta.outputs.version }}
