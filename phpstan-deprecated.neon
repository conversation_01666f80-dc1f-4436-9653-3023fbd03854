includes:
    - vendor/phpstan/phpstan-deprecation-rules/rules.neon
    - phpstan-baseline-deprecated.neon

parameters:
    phpVersion:
        min: 80200
        max: 80499

    customRulesetUsed: true
    reportUnmatchedIgnoredErrors: false
    resultCachePath: %tmpDir%/resultCache-deprecated.php

    paths:
        - app
        - bootstrap
        - cache
        - config
        - database
        - html
        - includes
        - LibreNMS
        - licenses
        - misc
        - resources
        - routes
        - storage
        - tests
