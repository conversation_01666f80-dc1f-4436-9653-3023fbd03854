<?php

namespace App\Console\Commands\ImportExtraCommands;

use App\Models\Device;
use App\Models\DeviceGroup;
use Arra\GroupSettings\Jobs\UpdateSettingsJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class ImportGroupConfigFileCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import-group-config-files';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import group config files';

    /**
     * Execute the console command.
     * @throws \Exception
     */
    public function handle()
    {
        $storage = Storage::disk('public');
        if (config('filesystems.default') === 's3') {
            $storage = Storage::disk('s3-private');
        }

        $deviceGroups = DeviceGroup::query()->get();

        foreach ($deviceGroups as $deviceGroup) {
            $sourceDir = storage_path('new-group-settings-files-import/' . $deviceGroup->id);

            // Check if source directory exists
            if (!is_dir($sourceDir)) {
                $this->info("Skipping group {$deviceGroup->id}: source directory not found");
                continue;
            }

            // Get all files from the source directory
            $files = glob($sourceDir . '/*');
            $files = array_filter($files, 'is_file');

            if (empty($files)) {
                $this->info("Skipping group {$deviceGroup->id}: no files found in source directory");
                continue;
            }

            // Clean up existing files
            $exists = $storage->exists('group-settings/'.$deviceGroup->id.'/'.'group.config');
            if ($exists) {
                $storage->delete('group-settings/'.$deviceGroup->id.'/'.'group.config');
            }

            $exists = $storage->exists('group-settings/'.$deviceGroup->id.'/'.'group_rev.config');
            if ($exists) {
                $storage->delete('group-settings/'.$deviceGroup->id.'/'.'group_rev.config');
            }

            // Upload each file from the source directory
            foreach ($files as $file) {
                $fileName = basename($file);
                $fileContent = file_get_contents($file);

                $destinationPath = 'group-settings/' . $deviceGroup->id . '/' . $fileName;
                $storage->put($destinationPath, $fileContent);

                $this->info("Uploaded {$fileName} for group {$deviceGroup->id}");
            }

            $devices = Device::inDeviceGroup($deviceGroup->id)->isUp()->get();

            foreach ($devices as $device) {
                UpdateSettingsJob::dispatch($device->device_id)->onQueue('device_actions');
            }
        }
    }
}
