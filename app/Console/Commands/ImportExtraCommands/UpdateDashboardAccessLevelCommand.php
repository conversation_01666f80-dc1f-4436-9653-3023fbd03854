<?php

namespace App\Console\Commands\ImportExtraCommands;

use App\Models\Dashboard;
use Illuminate\Console\Command;

class UpdateDashboardAccessLevelCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-dashboard-access-level';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     * @throws \Exception
     */
    public function handle()
    {
        Dashboard::where('access', 2)->update(['access' => 3]);
    }

}
