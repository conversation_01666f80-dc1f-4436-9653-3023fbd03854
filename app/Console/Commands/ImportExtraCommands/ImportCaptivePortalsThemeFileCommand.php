<?php

namespace App\Console\Commands\ImportExtraCommands;

use App\Models\Device;
use App\Models\DeviceGroup;
use Arra\CaptivePortalsManagement\Jobs\UpdateCaptivePortalJob;
use Arra\CaptivePortalsManagement\Models\ArraCaptivePortal;
use Arra\CaptivePortalsManagement\Repositories\ArraCPMRepository;
use Arra\CaptivePortalsManagement\Requests\UpdateCaptivePortalRequest;
use Arra\GroupSettings\Jobs\UpdateSettingsJob;
use Illuminate\Console\Command;
use Illuminate\Http\UploadedFile;

class ImportCaptivePortalsThemeFileCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import-cp-files';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import captive portal theme files';

    /**
     * Execute the console command.
     * @throws \Exception
     */
    public function handle()
    {
        $this->importThemeFiles();
    }

    /**
     * @throws \Exception
     */
    protected function importThemeFiles(): void
    {
        $zipFilePath = storage_path('new-cp-import/default/arra-cp.zip');
        $captivePortals = ArraCaptivePortal::query()->get();

        foreach ($captivePortals as $captivePortal) {
            $groupId = $captivePortal->device_group_id;
            try {
                // Create a temporary file from the zip content
                $tempFile = tempnam(sys_get_temp_dir(), 'arra-cp');
                copy($zipFilePath, $tempFile);

                // Create an UploadedFile instance
                $uploadedFile = new UploadedFile(
                    $tempFile,
                    'arra-cp.zip',
                    'application/zip',
                    null,
                    true // test mode - don't validate upload
                );

                // Create a mock request with the uploaded file
                $request = new UpdateCaptivePortalRequest();
                $request->files->set('themefile', $uploadedFile);

                // Call the repository method
                ArraCPMRepository::updateCaptivePortal($request, $groupId);

                // Dispatch job to update captive portal on all devices in the group
                $devices = Device::inDeviceGroup($groupId)->isUp()->get();

                foreach ($devices as $device) {
                    UpdateCaptivePortalJob::dispatch($device->device_id)->onQueue('device_actions');
                }

                // Clean up temp file
                @unlink($tempFile);
            } catch (\Exception $e) {
                $this->error('Failed to update captive portal: ' . $e->getMessage());
                throw $e;
            }
        }
    }
}
