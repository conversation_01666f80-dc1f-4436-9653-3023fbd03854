<?php

namespace App\Console\Commands\Import;

class ImportCaptivPortalToGroupsCommand extends BaseImportCommand
{
    protected $signature = 'import:captiv-portal-to-groups
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import captiv_portal_to_groups table (librenms_captiv_portal_to_groups → arra_captive_portals)';

    protected function getSourceTable(): string
    {
        return 'librenms_captiv_portal_to_groups';
    }

    protected function getTargetTable(): string
    {
        return 'arra_captive_portals';
    }

    protected function getColumnMapping(): array
    {
        return [
            'id' => 'id',
            'group_id' => 'device_group_id',
            'folder_name' => 'path',
            'version' => null, // Skip this column - target table doesn't have it
            'create_date' => 'created_at',
            'update_date' => 'updated_at',
        ];
    }
}
