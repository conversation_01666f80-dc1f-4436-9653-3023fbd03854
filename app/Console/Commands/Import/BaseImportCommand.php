<?php

namespace App\Console\Commands\Import;

use App\Console\LnmsCommand;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Exception;

abstract class BaseImportCommand extends LnmsCommand
{
    protected $signature = '{table}
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    private string $connection;
    private bool $isDryRun = false;

    abstract protected function getSourceTable(): string;
    abstract protected function getTargetTable(): string;
    abstract protected function getColumnMapping(): array;

    /**
     * Determine if chunked processing should be used for this import.
     * Can be overridden by subclasses to enable chunked processing by default.
     */
    protected function shouldUseChunkedProcessing(): bool
    {
        return $this->hasOption('chunked') && $this->option('chunked') || $this->isLargeDataset();
    }

    /**
     * Determine if this is a large dataset that would benefit from chunked processing.
     * Can be overridden by subclasses.
     */
    protected function isLargeDataset(): bool
    {
        // By default, let subclasses decide or use the --chunked flag
        return false;
    }

    /**
     * Get the chunk size for processing.
     */
    protected function getChunkSize(): int
    {
        return $this->hasOption('chunk-size') ? (int) $this->option('chunk-size') : 1000;
    }

    /**
     * Get the batch size for database insertions.
     */
    protected function getBatchSize(): int
    {
        return $this->hasOption('batch-size') ? (int) $this->option('batch-size') : 250;
    }

    /**
     * Determine if foreign key checks should be disabled during insertion.
     * Can be overridden by subclasses that need to handle missing foreign key references.
     */
    protected function shouldDisableForeignKeyChecks(): bool
    {
        return false;
    }

    public function handle(): int
    {
        $this->connection = $this->option('connection') ?: 'mysql';
        $this->isDryRun = $this->option('dry-run');

        $sourceTable = $this->getSourceTable();
        $targetTable = $this->getTargetTable();

        $this->info("Importing {$sourceTable} → {$targetTable}");

        if ($this->isDryRun) {
            $this->info('DRY-RUN mode - no data will be imported');
        }

        try {
            // Check if SQL file exists
            $sqlFile = base_path("prod-db/{$sourceTable}.sql");
            if (!File::exists($sqlFile)) {
                $this->warn("SQL file not found: {$sqlFile}");
                return 0;
            }

            // Check if target table exists
            if (!$this->targetTableExists()) {
                $this->warn("Target table {$targetTable} does not exist - skipping");
                return 0;
            }

            // Truncate if needed
            if (!$this->option('skip-truncate') && !$this->isDryRun) {
                $this->truncateTable();
            }

            // Get column mapping
            $mapping = $this->getColumnMapping();

            // Use chunked processing for large datasets or when explicitly requested
            if ($this->shouldUseChunkedProcessing()) {
                $totalImported = $this->processDataInChunks($sqlFile, $mapping);

                if ($totalImported === 0) {
                    $this->info("No data found in {$sourceTable}");
                    return 0;
                }

                if ($this->isDryRun) {
                    $this->info("Would import {$totalImported} records");
                    return 0;
                }

                $this->info("✓ Successfully imported {$totalImported} records");
                return 0;
            }

            // Regular processing for smaller datasets
            $data = $this->extractDataFromSql($sqlFile);

            if (empty($data)) {
                $this->info("No data found in {$sourceTable}");
                return 0;
            }

            // Transform data
            $transformedData = $this->transformData($data, $mapping);

            if ($this->isDryRun) {
                $this->info("Would import " . count($transformedData) . " records");
                if (count($transformedData) > 0) {
                    $this->info("Sample record: " . json_encode(array_slice($transformedData[0], 0, 3)));
                }
                return 0;
            }

            // Insert data
            if ($this->insertData($transformedData)) {
                $this->info("✓ Successfully imported " . count($transformedData) . " records");
                return 0;
            } else {
                return 1;
            }

        } catch (Exception $e) {
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
    }

    protected function targetTableExists(): bool
    {
        try {
            $connection = DB::connection($this->connection);
            $tables = $connection->select("SHOW TABLES LIKE '{$this->getTargetTable()}'");
            return !empty($tables);
        } catch (Exception $e) {
            return false;
        }
    }

    protected function truncateTable(): void
    {
        $connection = DB::connection($this->connection);
        $connection->statement('SET FOREIGN_KEY_CHECKS = 0');
        $connection->table($this->getTargetTable())->truncate();
        $connection->statement('SET FOREIGN_KEY_CHECKS = 1');
        $this->info("✓ Truncated {$this->getTargetTable()}");
    }

    protected function extractDataFromSql(string $sqlFile): array
    {
        $content = File::get($sqlFile);
        $data = [];

        // Find all INSERT INTO lines
        $lines = explode("\n", $content);

        foreach ($lines as $line) {
            if (strpos($line, 'INSERT INTO') !== false && strpos($line, 'VALUES') !== false) {
                // Extract column names and VALUES portion
                if (preg_match('/INSERT INTO\\s+`[^`]+`\\s+\\(([^)]+)\\)\\s+VALUES\\s*(.*?);?$/', $line, $matches)) {
                    $columnsStr = $matches[1];
                    $valuesStr = $matches[2];

                    // Parse column names
                    $columnNames = $this->parseColumnNames($columnsStr);

                    // Parse values and associate with column names
                    $rows = $this->parseValues($valuesStr, $columnNames);
                    $data = array_merge($data, $rows);
                }
            }
        }

        return $data;
    }

    protected function parseColumnNames(string $columnsStr): array
    {
        $columns = [];
        $parts = explode(',', $columnsStr);

        foreach ($parts as $part) {
            $part = trim($part);
            // Remove backticks if present
            $part = trim($part, '`');
            $columns[] = $part;
        }

        return $columns;
    }

    protected function parseValues(string $valuesStr, array $columnNames): array
    {
        $rows = [];
        $current = '';
        $inQuotes = false;
        $level = 0;

        for ($i = 0; $i < strlen($valuesStr); $i++) {
            $char = $valuesStr[$i];

            if ($char === "'" && ($i === 0 || $valuesStr[$i-1] !== '\\')) {
                $inQuotes = !$inQuotes;
                $current .= $char;
                continue;
            }

            if ($inQuotes) {
                $current .= $char;
                continue;
            }

            if ($char === '(') {
                $level++;
                if ($level === 1) {
                    $current = '';
                    continue;
                }
            }

            if ($char === ')') {
                $level--;
                if ($level === 0) {
                    $values = $this->parseRow($current);
                    // Associate values with column names
                    $row = [];
                    for ($j = 0; $j < count($columnNames) && $j < count($values); $j++) {
                        $row[$columnNames[$j]] = $values[$j];
                    }
                    $rows[] = $row;
                    $current = '';
                    continue;
                }
            }

            $current .= $char;
        }

        return $rows;
    }

    protected function parseRow(string $row): array
    {
        $values = [];
        $current = '';
        $inQuotes = false;

        for ($i = 0; $i < strlen($row); $i++) {
            $char = $row[$i];

            if ($char === "'" && ($i === 0 || $row[$i-1] !== '\\')) {
                $inQuotes = !$inQuotes;
                continue;
            }

            if (!$inQuotes && $char === ',') {
                $values[] = $this->convertValue($current);
                $current = '';
                continue;
            }

            $current .= $char;
        }

        // Always add the last value, even if it's empty
        $values[] = $this->convertValue($current);

        return $values;
    }

    protected function convertValue(string $value): mixed
    {
        $value = trim($value);

        if ($value === 'NULL') {
            return null;
        }

        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float) $value : (int) $value;
        }

        // Handle empty values that should be empty strings instead of null
        if ($value === '' || $value === '?') {
            return '';
        }

        // Remove excessive backslashes from escaped strings (common in SQL dumps)
        return stripslashes($value);
    }

    protected function transformData(array $data, array $columnMapping): array
    {
        $transformed = [];

        foreach ($data as $row) {
            $newRow = [];

            // Process all columns from mapping using column names
            foreach ($columnMapping as $sourceColumn => $targetColumn) {
                if ($targetColumn !== null) {
                    // Use value from row by column name if exists, otherwise null
                    $value = isset($row[$sourceColumn]) ? $row[$sourceColumn] : null;
                    $newRow[$targetColumn] = $value;
                }
            }

            $transformed[] = $newRow;
        }

        return $transformed;
    }

    protected function insertData(array $data): bool
    {
        if (empty($data)) {
            $this->info('No data to import');
            return true;
        }

        try {
            $connection = DB::connection($this->connection);
            $targetTable = $this->getTargetTable();

            // Disable foreign key checks if needed
            if ($this->shouldDisableForeignKeyChecks()) {
                $connection->statement('SET FOREIGN_KEY_CHECKS = 0');
            }

            // Insert data in batches to avoid MySQL placeholder limit
            $batchSize = 100; // Reduced batch size to avoid placeholder limit

            for ($i = 0; $i < count($data); $i += $batchSize) {
                $batch = array_slice($data, $i, $batchSize);
                $connection->table($targetTable)->insert($batch);
            }

            // Re-enable foreign key checks if they were disabled
            if ($this->shouldDisableForeignKeyChecks()) {
                $connection->statement('SET FOREIGN_KEY_CHECKS = 1');
            }

            return true;
        } catch (Exception $e) {
            $this->error("Failed to insert data: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Process data in chunks to avoid memory issues with large datasets.
     */
    protected function processDataInChunks(string $sqlFile, array $columnMapping): int
    {
        $content = File::get($sqlFile);
        $lines = explode("\n", $content);
        $totalImported = 0;
        $chunkSize = $this->getChunkSize();
        $batchSize = $this->getBatchSize();
        $currentChunk = [];

        foreach ($lines as $line) {
            if (strpos($line, 'INSERT INTO') !== false && strpos($line, 'VALUES') !== false) {
                // Extract column names and VALUES portion
                if (preg_match('/INSERT INTO\\s+`[^`]+`\\s+\\(([^)]+)\\)\\s+VALUES\\s*(.*?);?$/', $line, $matches)) {
                    $columnsStr = $matches[1];
                    $valuesStr = $matches[2];

                    // Parse column names
                    $columnNames = $this->parseColumnNames($columnsStr);

                    // Parse values and associate with column names
                    $rows = $this->parseValues($valuesStr, $columnNames);

                    foreach ($rows as $row) {
                        $currentChunk[] = $row;

                        // Process chunk when it reaches the size limit
                        if (count($currentChunk) >= $chunkSize) {
                            $imported = $this->processChunk($currentChunk, $columnMapping, $batchSize);
                            $totalImported += $imported;
                            $currentChunk = []; // Clear chunk to free memory

                            // Force garbage collection to free memory
                            if (function_exists('gc_collect_cycles')) {
                                gc_collect_cycles();
                            }
                        }
                    }
                }
            }
        }

        // Process remaining data in the last chunk
        if (!empty($currentChunk)) {
            $imported = $this->processChunk($currentChunk, $columnMapping, $batchSize);
            $totalImported += $imported;
        }

        return $totalImported;
    }

    /**
     * Process a single chunk of data.
     */
    protected function processChunk(array $chunk, array $columnMapping, int $batchSize): int
    {
        // Transform data
        $transformedData = $this->transformData($chunk, $columnMapping);

        if ($this->isDryRun) {
            return count($transformedData);
        }

        // Insert data in batches
        try {
            $connection = DB::connection($this->connection);
            $targetTable = $this->getTargetTable();

            // Disable foreign key checks if needed (only once per chunk)
            if ($this->shouldDisableForeignKeyChecks()) {
                $connection->statement('SET FOREIGN_KEY_CHECKS = 0');
            }

            $batches = array_chunk($transformedData, $batchSize);
            $imported = 0;

            foreach ($batches as $batch) {
                $connection->table($targetTable)->insert($batch);
                $imported += count($batch);
            }

            // Re-enable foreign key checks if they were disabled
            if ($this->shouldDisableForeignKeyChecks()) {
                $connection->statement('SET FOREIGN_KEY_CHECKS = 1');
            }

            $this->info("✓ Processed chunk: {$imported} records");
            return $imported;
        } catch (Exception $e) {
            $this->error("Failed to insert chunk: " . $e->getMessage());
            return 0;
        }
    }
}
