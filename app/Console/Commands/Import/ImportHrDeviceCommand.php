<?php

namespace App\Console\Commands\Import;

class ImportHrDeviceCommand extends BaseImportCommand
{
    protected $signature = 'import:hr-device
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import hrDevice table (librenms_hrDevice → hrDevice)';

    protected function getSourceTable(): string
    {
        return 'librenms_hrDevice';
    }

    protected function getTargetTable(): string
    {
        return 'hrDevice';
    }

    protected function getColumnMapping(): array
    {
        // Manual mapping based on actual SQL structure:
        // hrDevice_id, device_id, hrDeviceIndex, hrDeviceDescr, hrDeviceType, hrDeviceErrors, hrDeviceStatus, hrProcessorLoad
        return [
            'hrDevice_id' => 'hrDevice_id',
            'device_id' => 'device_id',
            'hrDeviceIndex' => 'hrDeviceIndex',
            'hrDeviceDescr' => 'hrDeviceDescr',
            'hrDeviceType' => 'hrDeviceType',
            'hrDeviceErrors' => 'hrDeviceErrors',
            'hrDeviceStatus' => 'hrDeviceStatus',
            'hrProcessorLoad' => 'hrProcessorLoad',
        ];
    }

    protected function shouldDisableForeignKeyChecks(): bool
    {
        // This import needs to disable foreign key checks to handle missing device references
        return true;
    }

    protected function transformData(array $data, array $columnMapping): array
    {
        $transformed = [];

        foreach ($data as $row) {
            $newRow = [];

            // Process all columns from mapping using column names
            foreach ($columnMapping as $sourceColumn => $targetColumn) {
                if ($targetColumn !== null) {
                    // Use value from row by column name if exists, otherwise null
                    $value = isset($row[$sourceColumn]) ? $row[$sourceColumn] : null;

                    // Handle special cases for NOT NULL constraints
                    if ($targetColumn === 'hrDeviceDescr' && $value === null) {
                        $value = '';
                    }
                    if ($targetColumn === 'hrDeviceStatus' && $value === null) {
                        $value = 'unknown';
                    }
                    if ($targetColumn === 'hrDeviceType' && $value === null) {
                        $value = 'unknown';
                    }

                    $newRow[$targetColumn] = $value;
                }
            }

            $transformed[] = $newRow;
        }

        return $transformed;
    }
}
