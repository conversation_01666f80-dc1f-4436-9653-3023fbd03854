<?php

namespace App\Console\Commands\Import;

class ImportEntityStateCommand extends BaseImportCommand
{
    protected $signature = 'import:entity-state
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import entityState table (librenms_entityState → entityState)';

    protected function getSourceTable(): string
    {
        return 'librenms_entityState';
    }

    protected function getTargetTable(): string
    {
        return 'entityState';
    }

    protected function getColumnMapping(): array
    {
        // Use manual mapping for entityState
        return [
            'entity_state_id' => 'entity_state_id',
            'device_id' => 'device_id',
            'entPhysical_id' => 'entPhysical_id',
            'subindex' => 'subindex',
            'group' => 'group',
            'key' => 'key',
            'value' => 'value',
        ];
    }
}
