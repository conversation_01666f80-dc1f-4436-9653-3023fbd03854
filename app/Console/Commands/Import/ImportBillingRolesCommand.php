<?php

namespace App\Console\Commands\Import;

class ImportBillingRolesCommand extends BaseImportCommand
{
    protected $signature = 'import:billing-roles
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import billing_roles table (librenms_billing_roles → arra_billing_roles)';

    protected function getSourceTable(): string
    {
        return 'librenms_billing_roles';
    }

    protected function getTargetTable(): string
    {
        return 'arra_billing_roles';
    }

    protected function getColumnMapping(): array
    {
        return [
            'id' => 'id', // Skip auto-increment ID
            'name' => 'name',
            'billing_role_id' => null, // Skip - not needed in target
            'created_at' => 'created_at',
            'updated_at' => 'updated_at',
        ];
    }
}
