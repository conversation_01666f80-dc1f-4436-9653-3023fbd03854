<?php

namespace App\Console\Commands\Import;

class ImportJuniAtmVpCommand extends BaseImportCommand
{
    protected $signature = 'import:juni-atm-vp
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import juniAtmVp table (librenms_juniAtmVp → juniAtmVp)';

    protected function getSourceTable(): string
    {
        return 'librenms_juniAtmVp';
    }

    protected function getTargetTable(): string
    {
        return 'juniAtmVp';
    }

    protected function getColumnMapping(): array
    {
        // Manual mapping based on actual SQL structure:
        // juniAtmVp_id, port_id, vp_id, vp_descr
        return [
            'juniAtmVp_id' => 'juniAtmVp_id',
            'port_id' => 'port_id',
            'vp_id' => 'vp_id',
            'vp_descr' => 'vp_descr',
        ];
    }

    protected function shouldDisableForeignKeyChecks(): bool
    {
        // This import needs to disable foreign key checks to handle missing port references
        return true;
    }
}
