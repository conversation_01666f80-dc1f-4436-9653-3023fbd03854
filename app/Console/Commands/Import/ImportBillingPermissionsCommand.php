<?php

namespace App\Console\Commands\Import;

class ImportBillingPermissionsCommand extends BaseImportCommand
{
    protected $signature = 'import:billing-permissions
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import billing_permissions table (librenms_billing_permissions → arra_billing_permissions)';

    protected function getSourceTable(): string
    {
        return 'librenms_billing_permissions';
    }

    protected function getTargetTable(): string
    {
        return 'arra_billing_permissions';
    }

    protected function getColumnMapping(): array
    {
        return [
            'id' => 'id', // Skip auto-increment ID
            'name' => 'name',
            'billing_id' => 'billing_platform_permission_id',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at',
        ];
    }
}
