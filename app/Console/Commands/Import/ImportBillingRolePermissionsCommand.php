<?php

namespace App\Console\Commands\Import;

class ImportBillingRolePermissionsCommand extends BaseImportCommand
{
    protected $signature = 'import:billing-role-permissions 
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import billing_role_permissions table (librenms_billing_role_permissions → arra_billing_role_permission)';

    protected function getSourceTable(): string
    {
        return 'librenms_billing_role_permissions';
    }

    protected function getTargetTable(): string
    {
        return 'arra_billing_role_permission';
    }

    protected function getColumnMapping(): array
    {
        return [
            'id' => null, // Skip auto-increment ID
            'billing_role_id' => 'arra_billing_role_id',
            'billing_permission_id' => 'arra_billing_permission_id',
            'created_at' => null, // Skip - target table doesn't have timestamps
            'updated_at' => null, // Skip - target table doesn't have timestamps
        ];
    }

    protected function shouldDisableForeignKeyChecks(): bool
    {
        return true; // Need to disable foreign key checks due to missing role ID 4
    }
}