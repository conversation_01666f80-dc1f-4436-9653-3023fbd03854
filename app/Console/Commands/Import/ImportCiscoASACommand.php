<?php

namespace App\Console\Commands\Import;

class ImportCiscoASACommand extends BaseImportCommand
{
    protected $signature = 'import:cisco-asa
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import ciscoASA table (librenms_ciscoASA → ciscoASA)';

    protected function getSourceTable(): string
    {
        return 'librenms_ciscoASA';
    }

    protected function getTargetTable(): string
    {
        return 'ciscoASA';
    }

    protected function getColumnMapping(): array
    {
        return [
            'ciscoASA_id' => 'ciscoASA_id',
            'device_id' => 'device_id',
            'oid' => 'oid',
            'data' => 'data',
            'high_alert' => 'high_alert',
            'low_alert' => 'low_alert',
            'disabled' => 'disabled',
        ];
    }
}
