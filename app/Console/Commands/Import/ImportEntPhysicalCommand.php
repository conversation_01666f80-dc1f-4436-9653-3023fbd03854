<?php

namespace App\Console\Commands\Import;

class ImportEntPhysicalCommand extends BaseImportCommand
{
    protected $signature = 'import:ent-physical
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target table}';

    protected $description = 'Import entPhysical table (librenms_entPhysical → entPhysical)';

    protected function getSourceTable(): string
    {
        return 'librenms_entPhysical';
    }

    protected function getTargetTable(): string
    {
        return 'entPhysical';
    }

    protected function getColumnMapping(): array
    {
        // Use manual mapping for entPhysical
        return [
            'entPhysical_id' => 'entPhysical_id',
            'device_id' => 'device_id',
            'entPhysicalIndex' => 'entPhysicalIndex',
            'entPhysicalDescr' => 'entPhysicalDescr',
            'entPhysicalClass' => 'entPhysicalClass',
            'entPhysicalName' => 'entPhysicalName',
            'entPhysicalHardwareRev' => 'entPhysicalHardwareRev',
            'entPhysicalFirmwareRev' => 'entPhysicalFirmwareRev',
            'entPhysicalSoftwareRev' => 'entPhysicalSoftwareRev',
            'entPhysicalAlias' => 'entPhysicalAlias',
            'entPhysicalAssetID' => 'entPhysicalAssetID',
            'entPhysicalIsFRU' => 'entPhysicalIsFRU',
            'entPhysicalModelName' => 'entPhysicalModelName',
            'entPhysicalVendorType' => 'entPhysicalVendorType',
            'entPhysicalSerialNum' => 'entPhysicalSerialNum',
            'entPhysicalContainedIn' => 'entPhysicalContainedIn',
            'entPhysicalParentRelPos' => 'entPhysicalParentRelPos',
            'entPhysicalMfgName' => 'entPhysicalMfgName',
            'ifIndex' => 'ifIndex',
        ];
    }
}
