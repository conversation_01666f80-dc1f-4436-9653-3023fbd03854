<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ImportAllTablesCommand extends Command
{
    protected $signature = 'import:all
                            {--dry-run : Preview changes without importing}
                            {--connection=mysql_copy : Database connection to use}
                            {--skip-truncate : Skip truncating target tables}
                            {--skip-cp-files : Skip importing captive portal files}';

    protected $description = 'Import all production data tables and captive portal files';

    /**
     * List of import commands to execute in order
     */
    protected array $importCommands = [
        // Core tables first
        'import:users',
        'import:locations',
        'import:device-groups',
        'import:devices',
        'import:device-group-device',

        // Device related data
        'import:devices-attribs',
        'import:devices-perms',
        'import:devices-group-perms',
        'import:device-graphs',
        'import:device-oids',
        'import:device-mibs',
        'import:device-perf',
        'import:device-relationships',
        'import:device-cmds',

        // Network infrastructure
        'import:links',
        'import:ports-fdb',
        'import:ports-adsl',
        'import:bgp-peers',
        'import:bgp-peers-cbgp',
        'import:ipv4-addresses',
        'import:ipv4-networks',
        'import:ipv4-mac',
        'import:ipv6-addresses',
        'import:ipv6-networks',
        'import:route',

        // Monitoring and metrics
        'import:access-points',
        'import:wireless-sensors',
        'import:mempools',
        'import:component',
        'import:component-prefs',
        'import:component-statuslog',
        'import:hr-device',
        'import:entity-state',
        'import:entphysical',
        'import:entphysical-state',

        // Applications
        'import:applications',
        'import:application-metrics',

        // Alerts and notifications
        'import:alert-rules',
        'import:alert-templates',
        'import:alert-schedulables',
        'import:alert-schedule',
        'import:alert-device-map',
        'import:alert-group-map',
        'import:alert-location-map',
        'import:alert-template-map',
        'import:alert-transports',
        'import:alert-transport-groups',
        'import:alert-transport-map',
        'import:alert-log',
        'import:alerts',

        // Billing
        'import:bills',
        'import:bill-data',
        'import:bill-history',
        'import:bill-perms',
        'import:bill-ports',
        'import:bill-port-counters',
        'import:customers',
        'import:billing-permissions',
        'import:billing-roles',
        'import:billing-role-permissions',
        'import:billing-security-alerts',

        // Dashboards and UI
        'import:dashboards',
        'import:widgets',
        'import:graph-types',

        // Group related
        'import:group-actions',
        'import:group-billing-details',
        'import:group-byod-devices',
        'import:group-dashboards',
        'import:group-settings',
        'import:group-tags',
        'import:group-users-email',

        // Mesh and planning
        'import:mesh-planner-nodes',
        'import:mesh-planner-lines',
        'import:mesh-planner-poi-import-infos',
        'import:arra-mesh-dynamics-devices-list',
        'import:arra-mesh-dynamics-device-details',
        'import:mesh-dynamics-discovery-queue',

        // Security and VPN
        'import:ipsec-tunnels',
        'import:cisco-asa',

        // Load balancers
        'import:loadbalancer-rservers',
        'import:loadbalancer-vservers',

        // System and configuration
        // 'import:config',
        'import:customoids',
        'import:mibdefs',
        'import:api-tokens',
        'import:authlog',
        'import:eventlog',
        'import:mac-accounting',
        'import:callback',

        // Specialized imports
        'import:ucd-diskio',
        'import:cef-switching',
        'import:juni-atm-vp',
        'import:mefinfo',
        'import:factory-devices',
        'import:init-devices',
        'import:run-init-devices',

        // Arra specific
        'import:arra-device-status',
        'import:arra-retry-ping',

        // Database schema (if needed)
        //'import:db-schema',
        //'import:migrations',
    ];

    public function handle()
    {
        $this->info('Starting complete production data import...');
        $this->newLine();

        $options = [];
        if ($this->option('dry-run')) {
            $options['--dry-run'] = true;
            $this->warn('Running in DRY-RUN mode - no data will be imported');
        }
        if ($this->option('connection')) {
            $options['--connection'] = $this->option('connection');
        }
        if ($this->option('skip-truncate')) {
            $options['--skip-truncate'] = true;
        }

        $successful = 0;
        $failed = 0;
        $failedCommands = [];

        // Execute all import commands
        foreach ($this->importCommands as $command) {
            $this->info("Executing: {$command}");

            try {
                $exitCode = $this->call($command, $options);
                if ($exitCode === 0) {
                    $this->info("✅ {$command} completed successfully");
                    $successful++;
                } else {
                    $this->error("❌ {$command} failed with exit code {$exitCode}");
                    $failed++;
                    $failedCommands[] = $command;
                }
            } catch (\Exception $e) {
                $this->error("❌ {$command} failed with exception: " . $e->getMessage());
                $failed++;
                $failedCommands[] = $command;
            }

            $this->newLine();
        }

        // Update dashboard access levels
        $this->info('Executing: update-dashboard-access-level');
        try {
            $exitCode = $this->call('update-dashboard-access-level');
            if ($exitCode === 0) {
                $this->info('✅ update-dashboard-access-level completed successfully');
                $successful++;
            } else {
                $this->error("❌ update-dashboard-access-level failed with exit code {$exitCode}");
                $failed++;
                $failedCommands[] = 'update-dashboard-access-level';
            }
        } catch (\Exception $e) {
            $this->error('❌ update-dashboard-access-level failed with exception: ' . $e->getMessage());
            $failed++;
            $failedCommands[] = 'update-dashboard-access-level';
        }
        $this->newLine();

        // Import captive portal files (unless skipped)
        if (!$this->option('skip-cp-files')) {
            $this->info('Executing: import-cp-files');
            try {
                $exitCode = $this->call('import-cp-files');
                if ($exitCode === 0) {
                    $this->info('✅ import-cp-files completed successfully');
                    $successful++;
                } else {
                    $this->error("❌ import-cp-files failed with exit code {$exitCode}");
                    $failed++;
                    $failedCommands[] = 'import-cp-files';
                }
            } catch (\Exception $e) {
                $this->error('❌ import-cp-files failed with exception: ' . $e->getMessage());
                $failed++;
                $failedCommands[] = 'import-cp-files';
            }
            $this->newLine();
        }

        // Import group config files
        $this->info('Executing: import-group-config-files');
        try {
            $exitCode = $this->call('import-group-config-files');
            if ($exitCode === 0) {
                $this->info('✅ import-group-config-files completed successfully');
                $successful++;
            } else {
                $this->error("❌ import-group-config-files failed with exit code {$exitCode}");
                $failed++;
                $failedCommands[] = 'import-group-config-files';
            }
        } catch (\Exception $e) {
            $this->error('❌ import-group-config-files failed with exception: ' . $e->getMessage());
            $failed++;
            $failedCommands[] = 'import-group-config-files';
        }
        $this->newLine();

        // Summary
        $this->info('=== Import Summary ===');
        $this->info("✅ Successful: {$successful}");
        $this->info("❌ Failed: {$failed}");

        if (!empty($failedCommands)) {
            $this->newLine();
            $this->error('Failed commands:');
            foreach ($failedCommands as $failedCommand) {
                $this->error("  - {$failedCommand}");
            }
        }

        return $failed === 0 ? 0 : 1;
    }
}
