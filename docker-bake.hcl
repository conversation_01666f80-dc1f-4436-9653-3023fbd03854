variable "DEFAULT_TAG" {
  default = "librenms:local"
}

variable "LIBRENMS_VERSION" {
  default = null
}

// Special target: https://github.com/docker/metadata-action#bake-definition
target "docker-metadata-action" {
  tags = ["${DEFAULT_TAG}"]
  args = {
    LIBRENMS_VERSION = LIBRENMS_VERSION
  }
}

// Default target if none specified
group "default" {
  targets = ["image-local"]
}

target "image" {
  inherits = ["docker-metadata-action"]
}

target "image-local" {
  inherits = ["image"]
  output = ["type=docker"]
}

target "image-all" {
  inherits = ["image"]
  platforms = [
    "linux/amd64",
    "linux/arm/v7",
    "linux/arm64",
    "linux/386",
    "linux/s390x"
  ]
}
