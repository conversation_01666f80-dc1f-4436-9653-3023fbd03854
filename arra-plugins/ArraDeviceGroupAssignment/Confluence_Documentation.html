<!DOCTYPE html>
<html>
<head>
    <title>ArraDeviceGroupAssignment Plugin Documentation</title>
</head>
<body>

<h1>ArraDeviceGroupAssignment Plugin Documentation</h1>

<p><strong>Plugin Name:</strong> ArraDeviceGroupAssignment<br>
<strong>Version:</strong> 1.0.0<br>
<strong>Compatibility:</strong> LibreNMS 10.x, 11.x<br>
<strong>PHP Version:</strong> ^8.2<br>
<strong>License:</strong> MIT</p>

<h2>Table of Contents</h2>
<ul>
    <li><a href="#overview">Overview</a></li>
    <li><a href="#features">Features</a></li>
    <li><a href="#architecture">Architecture</a></li>
    <li><a href="#installation">Installation</a></li>
    <li><a href="#configuration">Configuration</a></li>
    <li><a href="#usage">Usage</a></li>
    <li><a href="#api-integration">WHMCS API Integration</a></li>
    <li><a href="#business-models">Business Models Support</a></li>
    <li><a href="#logging">Logging</a></li>
    <li><a href="#troubleshooting">Troubleshooting</a></li>
    <li><a href="#api-reference">API Reference</a></li>
</ul>

<h2 id="overview">Overview</h2>
<p>The ArraDeviceGroupAssignment plugin is a LibreNMS extension that provides automated device group management with WHMCS billing system integration. It handles device assignment and removal operations with automatic billing system synchronization for open-access and monthly business models.</p>

<p>This plugin is designed to work seamlessly with the Arra Networks ecosystem, providing automated workflows for device management in network monitoring and billing environments.</p>

<h2 id="features">Features</h2>
<ul>
    <li><strong>🔄 Automated Device Assignment:</strong> Automatically handles device assignment to device groups with post-assignment actions</li>
    <li><strong>🗑️ Automated Device Removal:</strong> Manages device removal from groups with cleanup operations</li>
    <li><strong>💳 WHMCS Integration:</strong> Seamless integration with WHMCS billing system for automated billing operations</li>
    <li><strong>🌐 Open Access Support:</strong> Specialized handling for open-access business model with router configuration</li>
    <li><strong>📊 Comprehensive Logging:</strong> Detailed logging for all operations with dedicated log channels</li>
    <li><strong>🔒 Secure Communication:</strong> AES-256-CBC encryption for all WHMCS API communications</li>
</ul>

<h2 id="architecture">Architecture</h2>
<p>The plugin follows a service-oriented architecture with clear separation of concerns:</p>

<h3>Plugin Architecture Overview</h3>
<pre>
┌─────────────────────────────────────────────────────────────┐
│                    LibreNMS Application                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────┐   │
│  │           DeviceGroupAssignmentService              │   │
│  │              (Main Orchestrator)                    │   │
│  └─────────────────────────────────────────────────────┘   │
│                           │                                │
│  ┌─────────────────────┬─┴─────────────────────────────┐   │
│  │                     │                               │   │
│  ▼                     ▼                               │   │
│┌─────────────────┐ ┌─────────────────────────────┐    │   │
││SetRouterToOpen  │ │ RemoveRouterMacAddress      │    │   │
││AccessService    │ │ Service                     │    │   │
│└─────────────────┘ └─────────────────────────────┘    │   │
│         │                       │                      │   │
│         └───────────┬───────────┘                      │   │
│                     ▼                                  │   │
│            ┌─────────────────┐                         │   │
│            │  WhmcsConnector │                         │   │
│            │  (API Client)   │                         │   │
│            └─────────────────┘                         │   │
│                     │                                  │   │
│                     ▼                                  │   │
│            ┌─────────────────┐                         │   │
│            │   WHMCS API     │                         │   │
│            │  (Billing Sys)  │                         │   │
│            └─────────────────┘                         │   │
└─────────────────────────────────────────────────────────────┘
</pre>

<h3>Core Components</h3>
<ul>
    <li><strong>DeviceGroupAssignmentService:</strong> Main service that orchestrates device changes</li>
    <li><strong>SetRouterToOpenAccessService:</strong> Handles router configuration for open-access model</li>
    <li><strong>RemoveRouterMacAddressService:</strong> Manages router cleanup on device removal</li>
    <li><strong>WhmcsConnector:</strong> HTTP client for WHMCS API communication</li>
    <li><strong>BillingTrait:</strong> Provides encryption/decryption utilities</li>
    <li><strong>HelperTrait:</strong> Common utility functions</li>
</ul>

<h2 id="installation">Installation</h2>

<h3>Prerequisites</h3>
<ul>
    <li>LibreNMS 10.x or 11.x installed and configured</li>
    <li>PHP 8.2 or higher</li>
    <li>WHMCS billing system configured (for billing integration)</li>
    <li>Arra Group Settings plugin installed (for business model support)</li>
</ul>

<h3>Installation Steps</h3>

<p><strong>1. Download the plugin:</strong></p>
<pre>lnms plugin:add arra/device-group-assignment @dev</pre>

<p><strong>2. Enable the plugin:</strong></p>
<pre>lnms plugin:enable arra-device-group-assignment</pre>

<p><strong>3. Verify installation:</strong></p>
<pre>lnms about</pre>

<p><strong>Success!</strong> The plugin should now appear in the LibreNMS about page with version 1.0.0.</p>

<h3>Disabling the Plugin</h3>
<pre>lnms plugin:disable arra-device-group-assignment</pre>

<h2 id="configuration">Configuration</h2>

<h3>Required Database Tables</h3>
<p>The plugin requires the following database tables to be present:</p>

<table border="1">
    <tr>
        <th>Table Name</th>
        <th>Purpose</th>
        <th>Required By</th>
    </tr>
    <tr>
        <td><code>arra_group_settings</code></td>
        <td>Stores business model configuration and DNS settings</td>
        <td>All operations</td>
    </tr>
    <tr>
        <td><code>arra_device_group_billing_settings</code></td>
        <td>Stores WHMCS API configuration</td>
        <td>WHMCS integration</td>
    </tr>
    <tr>
        <td><code>devices</code></td>
        <td>LibreNMS devices table</td>
        <td>Device operations</td>
    </tr>
    <tr>
        <td><code>device_groups</code></td>
        <td>LibreNMS device groups table</td>
        <td>Group operations</td>
    </tr>
    <tr>
        <td><code>ports</code></td>
        <td>Device network interfaces</td>
        <td>MAC address extraction</td>
    </tr>
</table>



<h2 id="usage">Usage</h2>

<h3>How It Works</h3>
<p>The plugin automatically triggers when devices are added to or removed from device groups in LibreNMS. The process follows these steps:</p>

<ol>
    <li><strong>Device Assignment:</strong> When a device is added to a group
        <ul>
            <li>Plugin checks if the group has business model settings</li>
            <li>For open-access model: Configures router for open access</li>
            <li>Sends MAC address and configuration to WHMCS</li>
        </ul>
    </li>
    <li><strong>Device Removal:</strong> When a device is removed from a group
        <ul>
            <li>Plugin removes router configuration</li>
            <li>Sends removal request to WHMCS</li>
            <li>Cleans up billing associations</li>
        </ul>
    </li>
</ol>

<h3>Manual Integration</h3>
<p>You can manually trigger the service in your code:</p>

<pre>use Arra\DeviceGroupAssignment\Services\DeviceGroupAssignmentServiceInterface;

// Inject the service
$deviceGroupAssignmentService = app(DeviceGroupAssignmentServiceInterface::class);

// Handle device changes
$deviceGroupAssignmentService->handleDeviceChanges(
    attached: [1, 2, 3],    // Array of device IDs that were attached
    detached: [4, 5],       // Array of device IDs that were detached
    deviceGroupId: 1        // The device group ID
);</pre>

<h2 id="api-integration">WHMCS API Integration</h2>

<h3>API Endpoints</h3>
<p>The plugin communicates with WHMCS using the following endpoints:</p>

<table border="1">
    <tr>
        <th>Action</th>
        <th>Endpoint</th>
        <th>Purpose</th>
        <th>Parameters</th>
    </tr>
    <tr>
        <td><code>set_node_open_access</code></td>
        <td><code>index.php?m=arranetworkscore</code></td>
        <td>Configure router for open access</td>
        <td>node_mac, nms_group_id, open_access_parental_control, dns_primary, dns_secondary</td>
    </tr>
    <tr>
        <td><code>remove_node</code></td>
        <td><code>index.php?m=arranetworkscore</code></td>
        <td>Remove router from billing system</td>
        <td>node_mac</td>
    </tr>
</table>

<h3>Security</h3>
<p>All API communications are secured using AES-256-CBC encryption:</p>

<pre>// Encryption process
$data = json_encode($params);
$encrypt_method = "AES-256-CBC";
$key = hash('sha1', $billingKey);
$iv = substr(hash('sha1', $billingKey), 0, 16);
$encrypted = base64_encode(openssl_encrypt($data, $encrypt_method, $key, 0, $iv));</pre>

<h3>Request Format</h3>
<p>All requests are sent as POST requests with the following structure:</p>

<pre>{
    "token": "encrypted_data_here",
    "decoded_token": "json_encoded_parameters_here"
}</pre>

<h2 id="business-models">Business Models Support</h2>

<h3>Supported Models</h3>
<p>The plugin currently supports the following business models:</p>

<table border="1">
    <tr>
        <th>Business Model</th>
        <th>Description</th>
        <th>Actions</th>
    </tr>
    <tr>
        <td><code>open-access</code></td>
        <td>Free public WiFi access</td>
        <td>Router configuration, DNS setup, parental controls</td>
    </tr>
    <tr>
        <td><code>monthly</code></td>
        <td>Paid monthly subscriptions</td>
        <td>Billing integration, subscription management</td>
    </tr>
</table>

<h3>Open Access Configuration</h3>
<p>For open-access models, the plugin configures routers with:</p>
<ul>
    <li>DNS server settings (primary and secondary)</li>
    <li>Parental control settings (optional)</li>
    <li>MAC address registration in WHMCS</li>
</ul>

<h2 id="logging">Logging</h2>

<h3>Log Configuration</h3>
<p>The plugin uses a dedicated logging channel configured in <code>config/logging.php</code>:</p>

<pre>'channels' => [
    'arra-device-group-assignment-plugin' => [
        'driver' => 'single',
        'path' => base_path('logs/arra/arra-device-group-assignment-plugin/device-group-assignment.log'),
        'level' => env('LOG_LEVEL', 'debug'),
    ]
]</pre>

<h3>Log Messages</h3>
<p>The plugin logs various events with different severity levels:</p>

<table border="1">
    <tr>
        <th>Level</th>
        <th>Event</th>
        <th>Example Message</th>
    </tr>
    <tr>
        <td><code>INFO</code></td>
        <td>Device assignment/removal</td>
        <td>"Processing devices attached to group"</td>
    </tr>
    <tr>
        <td><code>WARNING</code></td>
        <td>Missing configuration</td>
        <td>"Billing settings not found for device group"</td>
    </tr>
    <tr>
        <td><code>ERROR</code></td>
        <td>Operation failures</td>
        <td>"Failed to process some device changes"</td>
    </tr>
    <tr>
        <td><code>DEBUG</code></td>
        <td>Detailed operations</td>
        <td>"Performing post-removal actions"</td>
    </tr>
</table>

<h3>Log Location</h3>
<p>Logs are stored in: <code>logs/arra/arra-device-group-assignment-plugin/device-group-assignment.log</code></p>

<h2 id="troubleshooting">Troubleshooting</h2>

<h3>Common Issues</h3>

<h4>1. Plugin Not Working</h4>
<p><strong>Symptoms:</strong> No actions triggered when devices are added/removed</p>
<p><strong>Solutions:</strong></p>
<ul>
    <li>Check if plugin is enabled: <code>lnms plugin:list</code></li>
    <li>Verify business model configuration in <code>arra_group_settings</code></li>
    <li>Check log files for errors</li>
</ul>

<h4>2. WHMCS Integration Failures</h4>
<p><strong>Symptoms:</strong> API calls to WHMCS failing</p>
<p><strong>Solutions:</strong></p>
<ul>
    <li>Verify billing URL and key in <code>arra_device_group_billing_settings</code></li>
    <li>Check network connectivity to WHMCS server</li>
    <li>Verify WHMCS module is installed and configured</li>
    <li>Check WHMCS logs for errors</li>
</ul>

<h4>3. Missing ETH0 Port</h4>
<p><strong>Symptoms:</strong> "ETH0 port not found for device" errors</p>
<p><strong>Solutions:</strong></p>
<ul>
    <li>Verify device has ETH0 interface configured</li>
    <li>Check SNMP discovery is working for the device</li>
    <li>Verify device is properly added to LibreNMS</li>
</ul>

<h4>4. Business Model Not Supported</h4>
<p><strong>Symptoms:</strong> "Invalid business model" warnings</p>
<p><strong>Solutions:</strong></p>
<ul>
    <li>Ensure business model is set to 'open-access' or 'monthly'</li>
    <li>Check <code>arra_group_settings</code> table configuration</li>
    <li>Verify Arra Group Settings plugin is installed</li>
</ul>

<h3>Debug Mode</h3>
<p>To enable debug logging, set the log level in your environment:</p>

<pre>LOG_LEVEL=debug</pre>

<h3>Manual Testing</h3>
<p>You can test the plugin manually by calling the service directly:</p>

<pre>// Test device assignment
$service = app(DeviceGroupAssignmentServiceInterface::class);
$service->handleDeviceChanges([1], [], 1); // Attach device 1 to group 1

// Test device removal
$service->handleDeviceChanges([], [1], 1); // Remove device 1 from group 1</pre>

<h2 id="api-reference">API Reference</h2>

<h3>DeviceGroupAssignmentServiceInterface</h3>

<h4>Methods</h4>

<h5>handleDeviceChanges(array $attached, array $detached, int $deviceGroupId): void</h5>
<p>Main method for handling device group changes.</p>

<table border="1">
    <tr>
        <th>Parameter</th>
        <th>Type</th>
        <th>Description</th>
    </tr>
    <tr>
        <td><code>$attached</code></td>
        <td><code>array</code></td>
        <td>Array of device IDs that were attached to the group</td>
    </tr>
    <tr>
        <td><code>$detached</code></td>
        <td><code>array</code></td>
        <td>Array of device IDs that were detached from the group</td>
    </tr>
    <tr>
        <td><code>$deviceGroupId</code></td>
        <td><code>int</code></td>
        <td>The ID of the device group</td>
    </tr>
</table>

<h3>SetRouterToOpenAccessService</h3>

<h4>Methods</h4>

<h5>execute(int $deviceGroupId, int $deviceId): ?array</h5>
<p>Configures a router for open access mode.</p>

<h3>RemoveRouterMacAddressService</h3>

<h4>Methods</h4>

<h5>execute(int $deviceGroupId, int $deviceId): ?array</h5>
<p>Removes a router's MAC address from the billing system.</p>

<h3>HelperTrait</h3>

<h4>Static Methods</h4>

<h5>clearUrl(string $url): string</h5>
<p>Cleans and normalizes URLs by removing leading/trailing slashes.</p>

<h5>addSeparatorToMacAddress(string $mac, string $separator = ":"): string</h5>
<p>Formats MAC addresses with proper separators.</p>

<h3>BillingTrait</h3>

<h4>Static Methods</h4>

<h5>encryptData(array $data, string $billingKey): string</h5>
<p>Encrypts data using AES-256-CBC for WHMCS API communication.</p>

<h5>decryptData(string $data, string $billingKey): array</h5>
<p>Decrypts data received from WHMCS API.</p>

<p><strong>Note:</strong> This documentation covers the core functionality of the ArraDeviceGroupAssignment plugin. For additional support or feature requests, please contact the Arra Networks development team.</p>

<hr>

<p><em>ArraDeviceGroupAssignment Plugin Documentation v1.0.0<br>
© 2024 Arra Networks. All rights reserved.</em></p>

</body>
</html> 