<?php

namespace Arra\DeviceGroupAssignment\Traits;

trait HelperTrait
{
    public static function clearUrl(string $url): string
    {
        return trim(rtrim(ltrim($url, '/'), '/'));
    }

    public static function addSeparatorToMacAddress(string $mac, $separator = ':'): string
    {
        if (str_contains($mac, (string) $separator)) {
            return $mac;
        }

        return implode($separator, str_split($mac, 2));
    }
}
