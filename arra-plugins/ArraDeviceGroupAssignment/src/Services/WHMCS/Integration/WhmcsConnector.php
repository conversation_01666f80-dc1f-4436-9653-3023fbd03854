<?php

namespace Arra\DeviceGroupAssignment\Services\WHMCS\Integration;

use Arra\DeviceGroupAssignment\Traits\HelperTrait;
use GuzzleHttp\Utils;
use Illuminate\Support\Facades\Log;

class WhmcsConnector
{
    use HelperTrait;

    /**
     * @throws \Exception
     */
    public function execute(WhmcsDtoRequest $request): float|object|array|bool|int|string|null
    {
        return $this->makeApiRequest(
            $request->getBillingApiUrl(),
            $request->getEndpoint(),
            $request->getMethod(),
            $request->getParams(),
            $request->getLogChannel()
        );
    }

    /**
     * @throws \Exception
     */
    private function makeApiRequest(
        string $billingApiUrl,
        string $endpoint,
        string $method = 'POST',
        array $params = [],
        string $logChannel = 'arra-device-group-assignment-plugin'
    ): ?array {
        $url = self::clearUrl($billingApiUrl).'/'.self::clearUrl($endpoint);

        switch ($method) {
            case 'POST':
                $ch = curl_init($url);
                $options = [
                    CURLOPT_POST            => true,
                    CURLOPT_RETURNTRANSFER  => true,
                    CURLOPT_POSTFIELDS => $params,
                ];
                if (config('app.env') === 'local') {
                    $options[CURLOPT_SSL_VERIFYPEER] = 0;
                    $options[CURLOPT_SSL_VERIFYHOST] = 0;
                }
                curl_setopt_array($ch, $options);
                break;
            case 'GET':
                $ch = curl_init($url.'&'.http_build_query($params));
                curl_setopt_array($ch, [
                    CURLOPT_RETURNTRANSFER  => true,
                ]);
                break;
            default:
                throw new \Exception('Invalid method');
        }

        $response = curl_exec($ch);

        if (curl_errno($ch) || $response === false) {
            $debug = curl_getinfo($ch);
            Log::channel($logChannel)->error('WHMCS API Error', [
                'url' => $url,
                'method' => $method,
                'params' => $params,
                'debug' => $debug,
            ]);
            throw new \Exception('Curl error: '.curl_error($ch));
        }

        Log::channel($logChannel)->notice('WHMCS API Response', [
            'url' => $url,
            'method' => $method,
            'params' => $params,
            'response' => $response,
        ]);

        return Utils::jsonDecode($response, true);
    }
}
