<?php

namespace Arra\DeviceGroupAssignment\Services\WHMCS\Integration;

use Arra\DeviceGroupAssignment\Services\WHMCS\Traits\BillingTrait;

class WhmcsDtoRequest
{
    use BillingTrait;

    public function __construct(
        private readonly string $billingApiUrl,
        private readonly string $endpoint,
        private readonly array $params,
        private readonly string $method = 'POST',
        private readonly string $logChannel = 'arra-device-group-assignment-plugin'
    ) {}

    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    public function getParams(): array
    {
        return $this->params;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getLogChannel(): string
    {
        return $this->logChannel;
    }

    public function getBillingApiUrl(): string
    {
        return $this->billingApiUrl;
    }
}
