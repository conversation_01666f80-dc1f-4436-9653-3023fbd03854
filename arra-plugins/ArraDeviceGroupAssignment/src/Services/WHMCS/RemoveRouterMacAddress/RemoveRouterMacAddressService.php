<?php

namespace Arra\DeviceGroupAssignment\Services\WHMCS\RemoveRouterMacAddress;

use App\Models\Port;
use Arra\DeviceGroupAssignment\Services\WHMCS\Integration\WhmcsConnector;
use Arra\DeviceGroupAssignment\Services\WHMCS\Integration\WhmcsDtoRequest;
use Arra\DeviceGroupAssignment\Services\WHMCS\Traits\BillingTrait;
use Arra\DeviceGroupAssignment\Traits\HelperTrait;
use Illuminate\Support\Facades\Log;

class RemoveRouterMacAddressService
{
    use BillingTrait, HelperTrait;

    public function __construct(private readonly WhmcsConnector $connector) {}

    /**
     * @throws \Exception
     */
    public function execute(int $deviceGroupId, int $deviceId): ?array
    {
        $billingSettings = \DB::table('arra_device_group_billing_settings')->where('device_group_id', $deviceGroupId)->first();
        if (empty($billingSettings)) {
            Log::channel('arra-device-group-assignment-plugin')->warning('Billing settings not found for device group', [
                'device_group_id' => $deviceGroupId,
            ]);

            return null;
        }
        $eth0Port = Port::where('device_id', $deviceId)->where('ifName', 'eth0')->first();
        if (empty($eth0Port)) {
            Log::channel('arra-device-group-assignment-plugin')->warning('ETH0 port not found for device', [
                'device_id' => $deviceId,
            ]);

            return null;
        }

        $routerMacAddress = self::addSeparatorToMacAddress($eth0Port->ifPhysAddress);
        $params = [
            'action' => 'remove_node',
            'node_mac' => $routerMacAddress,
            'timestamp' => time(),
        ];
        $endpoint = 'index.php?m=arranetworkscore';
        $whmcsDtoRequest = new WhmcsDtoRequest(
            billingApiUrl: $billingSettings->billing_url,
            endpoint: $endpoint,
            params: [
                'token' => self::encryptData($params, $billingSettings->billing_key),
                'decoded_token' => json_encode($params),
            ]
        );

        return $this->connector->execute($whmcsDtoRequest);
    }
}
