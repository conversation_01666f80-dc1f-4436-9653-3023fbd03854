<?php

namespace Arra\DeviceGroupAssignment\Services\WHMCS\Traits;

trait BillingTrait
{
    public static function encryptData(array $data, string $billingKey): string
    {
        $data = json_encode($data);
        $encrypt_method = 'AES-256-CBC';
        $key = hash('sha1', $billingKey);
        $iv = substr(hash('sha1', $billingKey), 0, 16);

        return base64_encode(openssl_encrypt($data, $encrypt_method, $key, 0, $iv));
    }

    public static function decryptData(string $data, string $billingKey): array
    {
        $encrypt_method = 'AES-256-CBC';
        $key = hash('sha1', $billingKey);
        $iv = substr(hash('sha1', $billingKey), 0, 16);

        return json_decode(openssl_decrypt(base64_decode($data), $encrypt_method, $key, 0, $iv), true);
    }
}
