<?php

namespace Arra\DeviceGroupAssignment\Services\WHMCS\SetRouterToOpenAccess;

use App\Models\Port;
use Arra\DeviceGroupAssignment\Services\WHMCS\Integration\WhmcsConnector;
use Arra\DeviceGroupAssignment\Services\WHMCS\Integration\WhmcsDtoRequest;
use Arra\DeviceGroupAssignment\Services\WHMCS\Traits\BillingTrait;
use Arra\DeviceGroupAssignment\Traits\HelperTrait;
use Illuminate\Support\Facades\Log;

class SetRouterToOpenAccessService
{
    use BillingTrait, HelperTrait;

    public function __construct(private readonly WhmcsConnector $connector) {}

    /**
     * @throws \Exception
     */
    public function execute(int $deviceGroupId, int $deviceId): ?array
    {
        $billingSettings = \DB::table('arra_device_group_billing_settings')->where('device_group_id', $deviceGroupId)->first();
        if (empty($billingSettings)) {
            Log::channel('arra-device-group-assignment-plugin')->warning('Billing settings not found for device group', [
                'device_group_id' => $deviceGroupId,
            ]);
            throw new \Exception('Billing settings not found for device group', 1);
        }
        $eth0Port = Port::where('device_id', $deviceId)->where('ifName', 'eth0')->first();
        if (empty($eth0Port)) {
            Log::channel('arra-device-group-assignment-plugin')->warning('ETH0 port not found for device', [
                'device_id' => $deviceId,
            ]);
            throw new \Exception('ETH0 port not found for device');
        }

        $routerMacAddress = self::addSeparatorToMacAddress($eth0Port->ifPhysAddress);
        $arraGroupSettings = \DB::table('arra_group_settings')->where('device_group_id', $deviceGroupId)->first();
        $dnsPrimary = '';
        $dnsSecondary = '';
        $openAccessParentalControl = 0;
        if (! empty($arraGroupSettings)) {
            $dnsPrimary = $arraGroupSettings->dns_primary;
            $dnsSecondary = $arraGroupSettings->dns_secondary;
            $businessModelValue = json_decode((string) $arraGroupSettings->business_model_value, true);
            if (! empty($businessModelValue['open-access-parental-control'])) {
                $openAccessParentalControl = 1;
            }
        }
        $params = [
            'action' => 'set_node_open_access',
            'node_mac' => $routerMacAddress,
            'nms_group_id' => $deviceGroupId,
            'open_access_parental_control' => $openAccessParentalControl,
            'dns_primary' => $dnsPrimary,
            'dns_secondary' => $dnsSecondary,
            'timestamp' => time(),
        ];
        $endpoint = 'index.php?m=arranetworkscore';
        $whmcsDtoRequest = new WhmcsDtoRequest(
            billingApiUrl: $billingSettings->billing_url,
            endpoint: $endpoint,
            params: [
                'token' => self::encryptData($params, $billingSettings->billing_key),
                'decoded_token' => json_encode($params),
            ]
        );

        return $this->connector->execute($whmcsDtoRequest);
    }
}
