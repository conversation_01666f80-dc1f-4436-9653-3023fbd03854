<?php

namespace Arra\DeviceGroupAssignment\Services;

use App\Models\Device;
use App\Models\DeviceGroup;
use Arra\DeviceGroupAssignment\Services\WHMCS\RemoveRouterMacAddress\RemoveRouterMacAddressService;
use Arra\DeviceGroupAssignment\Services\WHMCS\SetRouterToOpenAccess\SetRouterToOpenAccessService;
use Arra\DeviceGroupAssignment\Traits\HelperTrait;
use Illuminate\Support\Facades\Log;

class DeviceGroupAssignmentService implements DeviceGroupAssignmentServiceInterface
{
    use HelperTrait;

    public function __construct(
        private readonly RemoveRouterMacAddressService $removeRouterMacAddressService,
        private readonly SetRouterToOpenAccessService $setRouterToOpenAccessService
    ) {}

    /**
     * Perform actions after device assignment
     *
     * @return array|null
     * @throws \Exception
     */
    private function performPostAssignmentActions(Device $device, DeviceGroup $deviceGroup): ?array
    {
        Log::channel('arra-device-group-assignment-plugin')->info('Performing post-assignment actions', [
            'device_hostname' => $device->hostname,
            'device_group_name' => $deviceGroup->name,
        ]);

        return $this->setRouterToOpenAccessService->execute($deviceGroup->id, $device->device_id);
    }

    /**
     * Perform actions after device removal
     *
     * @return array|null
     * @throws \Exception
     */
    private function performPostRemovalActions(Device $device, DeviceGroup $deviceGroup): ?array
    {
        Log::channel('arra-device-group-assignment-plugin')->debug('Performing post-removal actions', [
            'device_hostname' => $device->hostname,
            'device_group_name' => $deviceGroup->name,
        ]);

        return $this->removeRouterMacAddressService->execute($deviceGroup->id, $device->device_id);

    }

    /**
     * Handle device changes after sync operation
     *
     * @param  array  $attached  Array of device IDs that were attached
     * @param  array  $detached  Array of device IDs that were detached
     * @param  int  $deviceGroupId  The device group ID
     * @throws \Exception When any operation fails
     */
    public function handleDeviceChanges(array $attached, array $detached, int $deviceGroupId): void
    {
        $deviceGroup = DeviceGroup::find($deviceGroupId);

        if (! $deviceGroup) {
            Log::channel('arra-device-group-assignment-plugin')->warning('DeviceGroup not found', [
                'device_group_id' => $deviceGroupId,
            ]);
            throw new \Exception("DeviceGroup with ID {$deviceGroupId} not found");
        }

        $arraGroupSettings = \DB::table('arra_group_settings')->where('device_group_id', $deviceGroup->id)->first();
        if (empty($arraGroupSettings)) {
            Log::channel('arra-device-group-assignment-plugin')->warning('Group settings not found for device group', [
                'device_group_id' => $deviceGroup->id,
            ]);

            // throw new \Exception("Group settings not found for device group");
            return;
        }

        if (! in_array($arraGroupSettings->business_model, ['open-access', 'monthly'])) {
            Log::channel('arra-device-group-assignment-plugin')->warning('Invalid business model', [
                'business_model' => $arraGroupSettings->business_model,
            ]);

            return;
        }

        $processedDevices = [];
        $errors = [];

        try {
            // Handle attached devices
            if (! empty($attached) && $arraGroupSettings->business_model == 'open-access') {
                Log::channel('arra-device-group-assignment-plugin')->info('Processing devices attached to group', [
                    'device_ids' => $attached,
                    'device_count' => count($attached),
                    'device_group_id' => $deviceGroupId,
                    'device_group_name' => $deviceGroup->name,
                ]);

                $attachedDevices = Device::whereIn('device_id', $attached)->get();
                foreach ($attachedDevices as $device) {
                    try {
                        $response = $this->performPostAssignmentActions($device, $deviceGroup);
                        if (! empty($response)) {
                            if ($response['success'] == 'true') {
                                $processedDevices[] = [
                                    'device_id' => $device->device_id,
                                    'action' => 'attached',
                                    'status' => 'success',
                                ];
                            } else {
                                $errors[] = [
                                    'device_id' => $device->device_id,
                                    'action' => 'attached',
                                    'error' => $response,
                                ];
                            }
                        }
                    } catch (\Throwable $e) {
                        $errors[] = [
                            'device_id' => $device->device_id,
                            'action' => 'attached',
                            'error' => $e->getMessage(),
                        ];
                        Log::error('Failed to process attached device', [
                            'device_id' => $device->device_id,
                            'device_hostname' => $device->hostname,
                            'error' => $e->getMessage(),
                        ]);
                    }
                }
            }

            // Handle detached devices
            if (! empty($detached)) {
                Log::channel('arra-device-group-assignment-plugin')->info('Processing devices detached from group', [
                    'device_ids' => $detached,
                    'device_count' => count($detached),
                    'device_group_id' => $deviceGroupId,
                    'device_group_name' => $deviceGroup->name,
                ]);

                $detachedDevices = Device::whereIn('device_id', $detached)->get();
                foreach ($detachedDevices as $device) {
                    try {
                        $response = $this->performPostRemovalActions($device, $deviceGroup);
                        if (! empty($response)) {
                            if ($response['success'] == 'true') {
                                $processedDevices[] = [
                                    'device_id' => $device->device_id,
                                    'action' => 'detached',
                                    'status' => 'success',
                                ];
                            } else {
                                $errors[] = [
                                    'device_id' => $device->device_id,
                                    'action' => 'detached',
                                    'error' => $response,
                                ];
                            }
                        }
                    } catch (\Throwable $e) {
                        $errors[] = [
                            'device_id' => $device->device_id,
                            'action' => 'detached',
                            'error' => $e->getMessage(),
                        ];
                        Log::channel('arra-device-group-assignment-plugin')->error('Failed to process detached device', [
                            'device_id' => $device->device_id,
                            'device_hostname' => $device->hostname,
                            'error' => $e->getMessage(),
                        ]);
                    }
                }
            }

            // If there are any errors, throw an exception with details
            if (! empty($errors)) {
                $errorMessage = 'Failed to process some device changes: '.json_encode($errors);
                Log::channel('arra-device-group-assignment-plugin')->error('Device changes processing failed', [
                    'attached' => $attached,
                    'detached' => $detached,
                    'device_group_id' => $deviceGroupId,
                    'errors' => $errors,
                    'processed' => $processedDevices,
                ]);
                throw new \Exception($errorMessage);
            }

            Log::channel('arra-device-group-assignment-plugin')->info('Device changes processed successfully', [
                'attached' => $attached,
                'detached' => $detached,
                'device_group_id' => $deviceGroupId,
                'processed_count' => count($processedDevices),
            ]);

        } catch (\Throwable $e) {
            Log::channel('arra-device-group-assignment-plugin')->error('Error handling device changes', [
                'attached' => $attached,
                'detached' => $detached,
                'device_group_id' => $deviceGroupId,
                'error' => $e->getMessage(),
                'processed_devices' => $processedDevices,
            ]);

            // Re-throw the exception to trigger rollback in the controller
            throw $e;
        }
    }
}
