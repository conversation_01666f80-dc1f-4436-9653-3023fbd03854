<?php

namespace Arra\DeviceGroupAssignment\Services;

interface DeviceGroupAssignmentServiceInterface
{
    /**
     * Handle device changes after sync operation
     *
     * @param  array  $attached  Array of device IDs that were attached
     * @param  array  $detached  Array of device IDs that were detached
     * @param  int  $deviceGroupId  The device group ID
     */
    public function handleDeviceChanges(array $attached, array $detached, int $deviceGroupId): void;
}
