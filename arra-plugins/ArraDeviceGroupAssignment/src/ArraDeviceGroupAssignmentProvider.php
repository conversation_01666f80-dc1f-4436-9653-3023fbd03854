<?php

namespace Arra\DeviceGroupAssignment;

use Arra\DeviceGroupAssignment\Services\DeviceGroupAssignmentService;
use Arra\DeviceGroupAssignment\Services\DeviceGroupAssignmentServiceInterface;
use Arra\DeviceGroupAssignment\Services\WHMCS\Integration\WhmcsConnector;
use Arra\DeviceGroupAssignment\Services\WHMCS\RemoveRouterMacAddress\RemoveRouterMacAddressService;
use Arra\DeviceGroupAssignment\Services\WHMCS\SetRouterToOpenAccess\SetRouterToOpenAccessService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Foundation\Console\AboutCommand;
use Illuminate\Support\ServiceProvider;
use LibreNMS\Interfaces\Plugins\PluginManagerInterface;

class ArraDeviceGroupAssignmentProvider extends ServiceProvider
{
    /**
     * Bootstrap any package services.
     */
    public function boot(PluginManagerInterface $pluginManager): void
    {
        $pluginName = 'arra-device-group-assignment';

        // register hooks with LibreNMS (if any are desired)
        // if no hooks are defined, LibreNMS may delete the plugin from the ui
        // if you don't want any specific hooks, you can just register a settings hook
        // $pluginManager->publishHook($pluginName, MenuEntryHook::class, MenuEntry::class);

        if (! $pluginManager->pluginEnabled($pluginName)) {
            return; // if plugin is disabled, don't boot
        }

        AboutCommand::add('Arra Device Group Assignment Plugin', fn (): array => ['Version' => '1.0.0']);
    }

    /**
     * @throws BindingResolutionException
     */
    public function register(): void
    {
        $this->mergeLoggingChannels();
        $this->bindDI();
    }

    /**
     * @throws BindingResolutionException
     */
    private function mergeLoggingChannels(): void
    {
        // This is the custom package logging configuration we just created earlier
        $packageLoggingConfig = require __DIR__.'/../config/logging.php';

        $config = $this->app->make('config');

        // For now we manually merge in only the logging channels. We could also merge other logging config here as well if needed.
        // We do this merging manually since mergeConfigFrom() does not do a deep merge and we want to merge only the channels array
        $config->set('logging.channels', array_merge(
            $packageLoggingConfig['channels'] ?? [],
            $config->get('logging.channels', [])
        ));
    }

    private function bindDI(): void
    {
        $this->app->singleton(WhmcsConnector::class, fn (Application $app) => new WhmcsConnector);

        $this->app->bindIf(SetRouterToOpenAccessService::class, fn (Application $app) => new SetRouterToOpenAccessService(
            connector: $app->make(WhmcsConnector::class)
        ));
        $this->app->bindIf(RemoveRouterMacAddressService::class, fn (Application $app) => new RemoveRouterMacAddressService(
            connector: $app->make(WhmcsConnector::class)
        ));
        $this->app->bindIf(DeviceGroupAssignmentServiceInterface::class, fn (Application $app) => new DeviceGroupAssignmentService(
            removeRouterMacAddressService: $app->make(RemoveRouterMacAddressService::class),
            setRouterToOpenAccessService: $app->make(SetRouterToOpenAccessService::class)
        ));
    }
}
