<?php

namespace Arra\SetDeviceGroup\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SetDeviceGroupRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'group' => ['required', 'numeric', 'exists:device_groups,id'],
            'host' => ['required', 'regex:/([a-fA-F0-9]{2}[:|\-]?){6}/'],
            'latlng' => ['required'],
        ];
    }

    public function messages(): array
    {
        return [
            'group.required' => 'Group is required',
            'group.numeric' => 'Group must be a number',
            'group.exists' => 'Group not found',
            'host.required' => 'Host is required',
            'host.regex' => 'Invalid host format',
            'latlng.required' => 'LatLng is required',
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
