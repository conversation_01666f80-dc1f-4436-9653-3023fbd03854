<?php

namespace Arra\SetDeviceGroup\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\DeviceGroup;

class IndexController extends Controller
{
    public function __invoke()
    {
        $groups = DeviceGroup::pluck('name', 'id')->all();

        // dd(DeviceGroup::first()->rules);
        return view('arra-set-device-group::index', [
            'groups' => $groups,
        ]);
    }
}
