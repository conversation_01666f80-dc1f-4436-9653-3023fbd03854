<?php

namespace Arra\SetDeviceGroup\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\DeviceGroup;
use Arra\SetDeviceGroup\Exceptions\DeviceAlreadyAssignedToThisGroupException;
use Arra\SetDeviceGroup\Http\Requests\SetDeviceGroupRequest;
use Arra\SetDeviceGroup\Services\SetDeviceGroupService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;

class SetDeviceGroupController extends Controller
{
    public function __invoke(SetDeviceGroupRequest $request)
    {
        $data = $request->validated();
        $group = DeviceGroup::find($data['group']);
        if (empty($group)) {
            return redirect()->route('arra.set-device-group')->with('error', 'Group not found');
        }

        $setDeviceGroupService = App::make(SetDeviceGroupService::class);

        try {
            DB::beginTransaction();
            $setDeviceGroupService->execute($data['group'], $data['host'], $data['latlng']);
            DB::commit();

            return redirect()->route('arra.set-device-group')->with('success', 'Device successfully added!');
        } catch (DeviceAlreadyAssignedToThisGroupException $e) {
            DB::rollback();

            return redirect()->route('arra.set-device-group')->with('error', $e->getMessage());
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error($e);

            return redirect()->route('arra.set-device-group')->with('error', 'Something went wrong!');
        }

    }
}
