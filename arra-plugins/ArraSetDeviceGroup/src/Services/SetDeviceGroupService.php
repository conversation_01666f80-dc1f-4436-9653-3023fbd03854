<?php

namespace Arra\SetDeviceGroup\Services;

use App\Models\Device;
use Arra\SetDeviceGroup\Exceptions\DeviceAlreadyAssignedToThisGroupException;
use Exception;

class SetDeviceGroupService
{
    public function __construct(
        private readonly SetDeviceAndLocationService $setDeviceAndLocationService,
        private readonly AddDeviceToGroupService $addDeviceToGroupService
    ) {}

    /**
     * @throws Exception|DeviceAlreadyAssignedToThisGroupException
     */
    public function execute(int $groupID, string $host, string $latlng): void
    {
        $hostFormat = $this->formatHost($host);

        $device = Device::where('hostname', 'like', "%{$hostFormat}")->first();
        if (empty($device)) {
            throw new Exception('Device not found');
        }

        $locationHostname = str_replace(['-', ':'], '', $host);
        $locationName = 'unassigned-'.substr($locationHostname, -6);

        $latlngArray = explode(',', $latlng);
        $latitude = $this->formatLatLng($latlngArray[0]);
        $longitude = $this->formatLatLng($latlngArray[1]);

        $this->setDeviceAndLocationService->execute($groupID, $device->device_id, $locationName, $latitude, $longitude);
        $this->addDeviceToGroupService->execute($groupID, $device->device_id, $hostFormat);

    }

    private function formatHost(string $host): string
    {
        $host = str_replace(['-', ':'], '', $host);

        return implode(':', str_split($host, 4));
    }

    private function formatLatLng(string $value): float
    {
        $value = (float) trim(strip_tags($value));

        return round($value, 6);
    }
}
