<?php

namespace Arra\SetDeviceGroup\Services;

use App\Models\Device;
use App\Models\Location;

class SetDeviceAndLocationService
{
    /**
     * @throws \Exception
     */
    public function execute(int $groupID, int $deviceID, string $locationName, float $latitude, float $longitude): void
    {
        $device = Device::find($deviceID);
        if (empty($device)) {
            throw new \Exception('Device not found');
        }
        $location = Location::where('location', $locationName)
            ->where(function ($query) use ($groupID): void {
                $query->where('device_group_id', $groupID);
                // ->orWhereNull('device_group_id');
            })
            ->first();

        if (empty($location)) {
            $location = new Location;
            $location->location = $locationName;
            $location->lat = $latitude;
            $location->lng = $longitude;
            $location->device_group_id = $groupID;
        } else {
            $location->lat = $latitude;
            $location->lng = $longitude;
        }
        $location->timestamp = date('Y-m-d H:i:s');
        $location->save();

        $device->location_id = $location->id;
        $device->save();
    }
}
