<?php

namespace Arra\SetDeviceGroup\Services;

use App\Models\Device;
use App\Models\DeviceGroup;
use Arra\SetDeviceGroup\Exceptions\DeviceAlreadyAssignedToThisGroupException;
use Exception;

class AddDeviceToGroupService
{
    /**
     * @throws Exception|DeviceAlreadyAssignedToThisGroupException
     */
    public function execute(int $groupID, int $deviceID, string $host): void
    {
        $group = DeviceGroup::find($groupID);
        if (empty($group)) {
            throw new Exception('Group not found');
        }

        $device = Device::find($deviceID);
        if (empty($device)) {
            throw new Exception('Device not found');
        }

        $rules = $group->rules;

        if (empty($rules)) {
            throw new Exception('Group rules not found');
        }

        foreach ($rules['rules'] as $rule) {
            if ($rule['value'] === $host && $rule['operator'] === 'ends_with') {
                throw new DeviceAlreadyAssignedToThisGroupException;
            }
        }

        $rules['rules'][] = [
            'id' => 'devices.hostname',
            'field' => 'devices.hostname',
            'type' => 'string',
            'input' => 'text',
            'operator' => 'ends_with',
            'value' => $host,
        ];
        $rules['condition'] = 'OR';

        $group->rules = $rules;
        $group->save();
    }
}
