<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        DB::statement('ALTER TABLE `librenms`.`locations` DROP INDEX IF EXISTS `locations_location_unique`, ADD UNIQUE `locations_location_group_unique` (`location`, `device_group_id`) USING BTREE; ');
    }

    public function down(): void
    {
        DB::statement('ALTER TABLE `librenms`.`locations` DROP INDEX IF EXISTS `locations_location_group_unique`, ADD UNIQUE `locations_location_unique` (`location`) USING BTREE; ');
    }
};
