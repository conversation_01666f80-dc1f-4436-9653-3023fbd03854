<div class="form-group group">
    <label for="theme-folder" class="control-label col-sm-3 col-md-2">@lang('Group')</label>
    <div class="col-sm-9 col-md-10">
        <select class="form-control" id="group_id" name="group">
            <option value="" >Please select Group</option>
            @foreach($groups as $group_id => $group_name)
                <option value="{{ $group_id }}" >{{ $group_name }}</option>
            @endforeach
        </select>
        @error('group')
            <span class="help-block" id="group-error"> {{ $message }} </span>
        @enderror

    </div>
</div>

<div class="form-group host">
    <label for="host" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Barcode MAC')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="host" name="host" value="">
        @error('host')
            <span class="help-block" id="host-error"> {{ $message }} </span>
        @enderror
    </div>
</div>

<div class="form-group latlng">
    <label for="latlng" class="control-label col-sm-3 col-md-2 text-nowrap">@lang('Lat/Lng')</label>
    <div class="col-sm-9 col-md-10">
        <input type="text" class="form-control" id="latlng" name="latlng" placeholder="Eg. 10, 10" value="">
        @error('latlng')
            <span class="help-block" id="latlng-error"> {{ $message }} </span>
        @enderror
    </div>
</div>
