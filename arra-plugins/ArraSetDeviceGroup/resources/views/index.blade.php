@extends('layouts.librenmsv1')

@section('content')
    <div class="container">
        <div class="row">
            @if (session('error'))
                <div class="alert alert-danger">{{ session('error') }}</div>
            @endif
            @if (session('success'))
                <div class="alert alert-success">{{ session('success') }}</div>
            @endif
            <form action="{{ route('arra.set-device-group.update') }}" method="POST" role="form"
                  class="form-horizontal col-md-10 col-md-offset-1 col-sm-12">
                <legend>{{ __('Set device group') }}</legend>
                {{ method_field('PUT') }}
                @csrf

                @include('arra-set-device-group::form')

                <div class="form-group">
                    <div class="col-sm-9 col-sm-offset-3 col-md-10 col-sm-offset-2">
                        <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                        <a type="button" class="btn btn-danger"
                           href="{{ url('/') }}">{{ __('Cancel') }}</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
@push('javascript')

    <script>
        $(document).ready(function () {
            $('#host').focus();
            $('#host').on("change keyup paste",function() {

                // handle events here
                var input_init = $( this ).val();
                var val_pices = input_init.split(":");
                if(val_pices.length == 1){

                    input_init = input_init.replace(/ /g,'');
                    input = input_init.replace(/:/g,'');
                    if(input.length == 12){
                        input = input.substring(0,12);
                        const first = input.substring(0,4);
                        const second = input.substring(4,8);
                        const third = input.substring(8,12);


                        if(input.length > 8){var output = `${first}:${second}:${third}`;}
                        else if(input.length > 4){var output = `${first}:${second}`;}
                        else if(input.length > 0){var output = `${first}`;}

                        $( this ).val(output);
                    }
                }
            });
        });
    </script>
@endpush

