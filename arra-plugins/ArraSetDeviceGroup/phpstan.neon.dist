includes:
    - vendor/larastan/larastan/extension.neon
    - vendor/nesbot/carbon/extension.neon

parameters:
    level: 5
    phpVersion:
        min: 80200
        max: 80499
    paths:
        - src/

    reportUnmatchedIgnoredErrors: false

    # Include LibreNMS autoloader to resolve dependencies
    bootstrapFiles:
        - ../../vendor/autoload.php
        - larastan-bootstrap.php

    # Scan additional directories for class discovery
    scanDirectories:
        - ../../LibreNMS
        - ../../includes
        - src/Models

