<?php

use Arra\SetDeviceGroup\Http\Controllers\IndexController;
use Arra\SetDeviceGroup\Http\Controllers\SetDeviceGroupController;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'auth'])->group(function (): void {
    Route::get('arra/set-device-group', IndexController::class)->name('arra.set-device-group');
    Route::put('arra/set-device-group', SetDeviceGroupController::class)->name('arra.set-device-group.update');
});
