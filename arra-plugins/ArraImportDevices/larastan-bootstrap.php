<?php

declare(strict_types=1);

// Custom bootstrap file for Larastan to ensure LARAVEL_VERSION is defined

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Contracts\Foundation\Application;
use Lara<PERSON>\Lumen\Application as LumenApplication;

if (! defined('LARAVEL_START')) {
    define('LARAVEL_START', microtime(true));
}

// Try to find Laravel application
$app = null;

// Check for LibreNMS bootstrap
if (file_exists($applicationPath = __DIR__.'/../../bootstrap/app.php')) {
    $app = require $applicationPath;
} elseif (file_exists($applicationPath = getcwd().'/bootstrap/app.php')) {
    $app = require $applicationPath;
} elseif (file_exists($applicationPath = dirname(__DIR__, 3).'/bootstrap/app.php')) {
    $app = require $applicationPath;
}

if (isset($app)) {
    if ($app instanceof Application) {
        $app->make(Kernel::class)->bootstrap();
    } elseif ($app instanceof LumenApplication) {
        $app->boot();
    }

    if (! defined('LARAVEL_VERSION')) {
        define('LARAVEL_VERSION', $app->version());
    }
} else {
    // Fallback: Define a default Laravel version if we can't find the application
    if (! defined('LARAVEL_VERSION')) {
        // Try to get version from composer.lock or use a reasonable default
        $composerLock = __DIR__.'/../../composer.lock';
        $version = '12.0.0'; // Default fallback

        if (file_exists($composerLock)) {
            $lockData = json_decode(file_get_contents($composerLock), true);
            if (isset($lockData['packages'])) {
                foreach ($lockData['packages'] as $package) {
                    if ($package['name'] === 'laravel/framework') {
                        $version = ltrim($package['version'], 'v');
                        break;
                    }
                }
            }
        }

        define('LARAVEL_VERSION', $version);
    }
}
