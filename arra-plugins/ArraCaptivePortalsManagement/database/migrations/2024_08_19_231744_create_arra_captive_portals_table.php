<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasTable('arra_captive_portals')) {
            Schema::create('arra_captive_portals', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('device_group_id')->unique();
                $table->string('path')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('arra_captive_portals');
    }
};
