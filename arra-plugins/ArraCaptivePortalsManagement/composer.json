{"name": "arra/captive-portals-management", "description": "LibreNMS Arra Captive Portals Management Plugin", "keywords": ["librenms", "plugin", "arra"], "type": "package", "license": "MIT", "authors": [{"name": "Arra Networks"}], "autoload": {"psr-4": {"Arra\\CaptivePortalsManagement\\": "src/"}, "files": ["./helpers.php"]}, "autoload-dev": {"psr-4": {"Arra\\CaptivePortalsManagement\\Tests\\": "tests"}}, "require": {"php": "^8.2", "librenms/plugin-interfaces": "^1.0"}, "suggest": {"illuminate/support": "for the Laravel integration", "illuminate/contracts": "for the Laravel integration"}, "require-dev": {"laravel/framework": "^12.10", "laravel/pint": "^1.13.7", "mockery/mockery": "^1.6", "pestphp/pest": "^3.0", "phpunit/phpunit": "^11.5.3", "rector/rector": "^2.0", "larastan/larastan": "^3.0"}, "extra": {"laravel": {"providers": ["Arra\\CaptivePortalsManagement\\CaptivePortalsManagementProvider"], "aliases": {"ArraCPM": "Arra\\CaptivePortalsManagement\\Facade"}}}, "scripts": {"refactor": "rector", "test:refactor": "rector process --dry-run --", "test:types": "phpstan analyse --ansi", "test:unit": "pest --colors=always", "test:lint": "pint --test", "test": ["@test:refactor", "@test:lint", "@test:types", "@test:unit"], "fix:lint": "pint --preset laravel", "fix:refactor": "rector", "fix": ["@fix:refactor", "@fix:lint"]}, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}