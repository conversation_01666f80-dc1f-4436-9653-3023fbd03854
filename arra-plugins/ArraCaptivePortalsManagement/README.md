# Introduction

This plugin manage the captive portals on routers.

Full documentation [HERE](https://arranetworks.atlassian.net/wiki/spaces/AW/pages/181501954/ArraCaptivePortalsManagement+Plugin+Documentation)

# Installation

Download the plugin by running the following command:
```bash
lnms plugin:add arra/captive-portals-management @dev
```

Migrate the database by running the following command:
```bash
lnms migrate
```

To enable the plugin, run the following command:
```bash
lnms plugin:enable arra/captive-portals-management
```

To disable the plugin, run the following command:
```bash
lnms plugin:disable arra/captive-portals-management
```

