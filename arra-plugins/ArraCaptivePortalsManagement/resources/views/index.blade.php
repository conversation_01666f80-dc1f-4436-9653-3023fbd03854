
@extends('layouts.librenmsv1')

@section('title', __('Captive Portals Management'))

@section('content')
    <div class="container-fluid">
        <x-panel id="manage-captive-portals-panel">
            <x-slot name="title">
                <i class="fa fa-bars fa-fw fa-lg" aria-hidden="true"></i> {{ __('Captive Portals Management') }}
            </x-slot>
            <div class="table-responsive">
                <table id="manage-captive-portals-table" class="table table-condensed table-hover">
                    <thead>
                    <tr style="background: transparent !important;">
                        <th>@lang('Group')</th>
                        <th>@lang('Has Captive Portal')</th>
                        <th>@lang('Preview')</th>
                        <th>@lang('Devices')</th>
                        <th>@lang('Actions')</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($captivePortals as $captivePortal)
                        <tr id="row_{{ $captivePortal->id }}" style="background: transparent !important;">
                            <td>{{ $captivePortal->deviceGroup->name }}</td>
                            <td>
                                @if(!empty($captivePortal->downloadUrl))
                                    Yes
                                @else
                                    -
                                @endif</td>
                            <td>
                                @if(!empty($captivePortal->previewUrl))
                                    <a href="{{$captivePortal->previewUrl}}" target="_blank" class="interface-upup">Preview</a>
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                <a href="{{ url("/devices/group={$captivePortal->deviceGroup->id}") }}" class="interface-upup">{{ $captivePortal->deviceGroup->devices->count() }}</a>
                            </td>
                            <td>
                                <a type="button" title="{{ __('Edit Captive Portal') }}" class="btn btn-primary btn-sm" aria-label="{{ __('Edit') }}"
                                   href="{{ route('arra-captive-portals-management.edit', $captivePortal->id) }}">
                                    <i class="fa fa-pencil" aria-hidden="true"></i></a>
                                <button type="button" class="btn btn-danger btn-sm" title="{{ __('Delete Captive Portal') }}" aria-label="{{ __('Delete') }}"
                                        onclick="delete_cp(this, '{{ $captivePortal->deviceGroup->name }}', '{{ route('arra-captive-portals-management.delete', $captivePortal->id) }}')">
                                    <i
                                        class="fa fa-trash" aria-hidden="true"></i></button>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </x-panel>
    </div>
@endsection

@section('scripts')
    <script>
        function delete_cp(button, name, url) {
            var index = button.parentNode.parentNode.rowIndex;

            if (confirm('{{ __('Are you sure you want to delete Captive Portal for group ') }}' + name + '?')) {
                $.ajax({
                    url: url,
                    type: 'DELETE',
                    success: function (msg) {
                        document.getElementById("manage-captive-portals-table").deleteRow(index);
                        toastr.success(msg);
                    },
                    error: function () {
                        toastr.error('{{ __('The captive portal could not be deleted') }}');
                    }
                });
            }

            return false;
        }
    </script>
@endsection

@section('css')
    <style>
        .table-responsive {
            padding-top: 16px
        }
    </style>
@endsection
