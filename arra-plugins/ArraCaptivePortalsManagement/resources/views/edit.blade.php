@extends('layouts.librenmsv1')

@section('title', __('Edit Captive Portal'))

@section('content')
    <div class="container">
        <div class="row">
            <form action="{{ route('arra-captive-portals-management.update', $captivePortal->id) }}" method="POST" enctype="multipart/form-data" role="form"
                  class="form-horizontal device-group-form col-md-10 col-md-offset-1 col-sm-12">
                <legend>{{ __('Edit Captive Portal') }}: {{ $captivePortal->deviceGroup->name }}</legend>
                {{ method_field('PUT') }}
                @csrf

                @include('arra-captive-portals-management::form')

                <div class="form-group">
                    <div class="col-sm-9 col-sm-offset-3 col-md-10 col-sm-offset-2">
                        <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                        <a type="button" class="btn btn-danger"
                           href="{{ route('arra-captive-portals-management.index') }}">{{ __('Cancel') }}</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('javascript')
    <script src="{{ asset('js/sql-parser.min.js') }}"></script>
    <script src="{{ asset('js/query-builder.standalone.min.js') }}"></script>
@endsection
