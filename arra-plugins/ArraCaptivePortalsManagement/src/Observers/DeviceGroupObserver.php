<?php

namespace Arra\CaptivePortalsManagement\Observers;

use App\Models\DeviceGroup;
use Arra\CaptivePortalsManagement\Models\ArraCaptivePortal;

class DeviceGroupObserver
{
    /**
     * Handle the device "created" event.
     */
    public function created(DeviceGroup $deviceGroup): void
    {
        $arraCaptivePortal = ArraCaptivePortal::where('device_group_id', $deviceGroup->id)->first();
        if ($arraCaptivePortal) {
            $arraCaptivePortal->delete();
        }
        $arraCaptivePortal = new ArraCaptivePortal;
        $arraCaptivePortal->device_group_id = $deviceGroup->id;
        $arraCaptivePortal->save();
    }
}
