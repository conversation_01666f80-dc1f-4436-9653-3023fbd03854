<?php

namespace Arra\CaptivePortalsManagement\Models;

use App\Models\DeviceGroup;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $device_group_id
 * @property string|null $path
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property-read DeviceGroup $deviceGroup
 *
 * Dynamic properties (added at runtime):
 * @property string|null $downloadUrl
 * @property string|null $previewUrl
 */
class ArraCaptivePortal extends Model
{
    protected $table = 'arra_captive_portals';

    public function deviceGroup(): BelongsTo
    {
        return $this->belongsTo(DeviceGroup::class, 'device_group_id');
    }
}
