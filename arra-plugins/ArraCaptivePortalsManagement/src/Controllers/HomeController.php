<?php

namespace Arra\CaptivePortalsManagement\Controllers;

use App\Models\Device;
use Arra\CaptivePortalsManagement\Jobs\UpdateCaptivePortalJob;
use Arra\CaptivePortalsManagement\Models\ArraCaptivePortal;
use Arra\CaptivePortalsManagement\Repositories\ArraCPMRepository;
use Arra\CaptivePortalsManagement\Requests\UpdateCaptivePortalRequest;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Storage;
use Response;

class HomeController extends Controller
{
    public function index()
    {
        $captivePortals = ArraCaptivePortal::with('deviceGroup.devices')->whereHas('deviceGroup')->get();
        foreach ($captivePortals as $captivePortal) {
            $captivePortal->downloadUrl = ArraCPMRepository::getCPDownloadUrl($captivePortal->device_group_id);
            $captivePortal->previewUrl = ArraCPMRepository::getCPPreviewUrl($captivePortal->device_group_id);
        }

        return view('arra-captive-portals-management::index', [
            'captivePortals' => $captivePortals,
        ]);
    }

    public function gets3PrivateFiles()
    {
        $files = Storage::disk('s3-private')->allFiles();
        // dd($files); // Debug line - uncomment if needed

        return response()->json($files);
    }

    public function gets3PublicFiles()
    {
        $files = Storage::disk('s3')->allFiles();
        // dd($files); // Debug line - uncomment if needed

        return response()->json($files);
    }

    public function edit(Request $request, ArraCaptivePortal $captivePortal)
    {
        $captivePortal = ArraCaptivePortal::whereHas('deviceGroup')->with('deviceGroup')->find($captivePortal->id);

        return view('arra-captive-portals-management::edit', [
            'captivePortal' => $captivePortal,
        ]);
    }

    /**
     * @throws \Exception
     */
    public function update(UpdateCaptivePortalRequest $request, ArraCaptivePortal $captivePortal)
    {
        ArraCPMRepository::updateCaptivePortal($request, $captivePortal->id);
        $devices = Device::inDeviceGroup($captivePortal->device_group_id)->isUp()->get();

        foreach ($devices as $device) {
            UpdateCaptivePortalJob::dispatch($device->device_id)->onQueue('device_actions');
        }
        if ($request->ajax()) {
            return response()->json([
                'message' => 'Captive portal updated successfully',
            ]);
        }

        return redirect()->route('arra-captive-portals-management.index')->with('success', 'Captive portal updated successfully');
    }

    public function delete(Request $request, ArraCaptivePortal $captivePortal)
    {
        ArraCPMRepository::deleteOldCPFiles($captivePortal->device_group_id);
        $captivePortal->delete();
        if ($request->ajax()) {
            return response()->json([
                'message' => 'Captive portal deleted successfully',
            ]);
        }

        return redirect()->route('arra-captive-portals-management.index')->with('success', 'Captive portal deleted successfully');
    }

    public function getCPThemeByMacAddress(Request $request)
    {
        if (! $request->has('mac') || ! $request->filled('mac')) {
            return '';
        }

        $macAddress = $request->input('mac');

        $macAddress = macAddressFormat($macAddress);

        $device = Device::where('hostname', 'like', "%{$macAddress}")->with(['groups'])->first();
        if (! $device) {
            return '';
        }

        $deviceGroup = $device->groups->first();

        if (! $deviceGroup) {
            return '';
        }

        header('Location: '.ArraCPMRepository::getCPDownloadUrl($deviceGroup->id));

        // return Response::redirectTo(ArraCPMRepository::getCPDownloadUrl($deviceGroup->id));
    }

    public function getCPVersion(Request $request)
    {
        if (config('app.debug')) {
            \Debugbar::disable();
        }

        if (! $request->has('mac') || ! $request->filled('mac')) {
            return '';
        }

        $macAddress = $request->input('mac');

        $macAddress = macAddressFormat($macAddress);

        $device = Device::where('hostname', 'like', "%{$macAddress}")->with(['groups'])->first();
        if (! $device) {
            return '';
        }

        $deviceGroup = $device->groups->first();

        if (! $deviceGroup) {
            return '';
        }

        return response(hash_file('sha256', ArraCPMRepository::getCPDownloadUrl($deviceGroup->id)))->header('Access-Control-Allow-Origin', '*');
    }
}
