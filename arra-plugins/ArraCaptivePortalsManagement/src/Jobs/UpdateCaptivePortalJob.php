<?php

namespace Arra\CaptivePortalsManagement\Jobs;

use App\Models\Device;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateCaptivePortalJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $device;

    /**
     * Create a new job instance.
     */
    public function __construct(private int $deviceID)
    {
        $this->device = Device::with(['groups'])->find($this->deviceID);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (! empty($this->device)) {
            $deviceCommand = 'echo "{\'action\':\'update_captive_portal\'}" | nc -w6 '.$this->device->hostname.' 1080';
            exec($deviceCommand, $output);
            if (! empty($output)) {
                Log::channel('rmq_device_action')->notice("Update Captive Portal Job for device {device}!\n Run command: {deviceCommand}\n Received output: {output}\n", ['device' => $this->device->hostname, 'deviceCommand' => $deviceCommand, 'output' => $output]);
            } else {
                Log::channel('rmq_device_action')->error("ERROR Update Captive Portal Job for device {device}!\n Run command: {deviceCommand}\n Received output: {output}\n", ['device' => $this->device->hostname, 'deviceCommand' => $deviceCommand, 'output' => $output]);
            }
        } else {
            Log::channel('rmq_device_action')->error("ERROR Update Captive Portal Job for device with ID {deviceID}!\n Device not found\n", ['deviceID' => $this->deviceID]);
        }
    }
}
