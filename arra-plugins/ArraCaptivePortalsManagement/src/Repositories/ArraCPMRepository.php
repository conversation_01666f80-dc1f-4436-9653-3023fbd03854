<?php

namespace Arra\CaptivePortalsManagement\Repositories;

use Arra\CaptivePortalsManagement\Models\ArraCaptivePortal;
use Arra\CaptivePortalsManagement\Requests\UpdateCaptivePortalRequest;
use Illuminate\Support\Facades\Storage;

class ArraCPMRepository
{
    protected static string $disk;

    public static function initializeDisk(): void
    {
        self::$disk = config('filesystems.default') === 'local' ? 'public' : config('filesystems.default');
    }

    public static function getCPDownloadUrl(int $deviceGroupId): string
    {
        if (empty(self::$disk)) {
            self::initializeDisk();
        }

        $fileName = 'arra-cp.zip';
        try {
            $storage = Storage::disk(self::$disk);
            $captivePortal = ArraCaptivePortal::where('device_group_id', $deviceGroupId)->firstOrFail();
            if ($storage->exists('captive_portals/'.$captivePortal->device_group_id.'/'.$fileName)) {
                return $storage->url('captive_portals/'.$captivePortal->device_group_id.'/'.$fileName);
            }

            return '';
        } catch (\Exception) {
            return '';
        }
    }

    public static function getCPPreviewUrl(int $deviceGroupId): string
    {
        if (empty(self::$disk)) {
            self::initializeDisk();
        }

        $fileName = 'preview/arra-cp/index.html';
        try {
            $storage = Storage::disk(self::$disk);
            $captivePortal = ArraCaptivePortal::where('device_group_id', $deviceGroupId)->whereNotNull('path')->firstOrFail();
            if ($storage->exists('captive_portals/'.$captivePortal->device_group_id.'/'.$fileName)) {
                return $storage->url('captive_portals/'.$captivePortal->device_group_id.'/'.$fileName);
            }

            return '';
        } catch (\Exception) {
            return '';
        }
    }

    /**
     * @throws \Exception
     */
    public static function updateCaptivePortal(UpdateCaptivePortalRequest $request, int $id): void
    {
        if (empty(self::$disk)) {
            self::initializeDisk();
        }

        $captivePortalModel = ArraCaptivePortal::with('deviceGroup')->find($id);
        $fileName = 'arra-cp.zip';
        $path = 'captive_portals/'.$captivePortalModel->device_group_id;
        $storage = Storage::disk(self::$disk);
        $localStorage = Storage::disk('local');

        self::deleteOldCPFiles($captivePortalModel->device_group_id);

        if (! $storage->putFileAs($path, $request->file('themefile'), $fileName)) {
            $captivePortalModel->path = null;
            $captivePortalModel->save();
            throw new \Exception('Failed to upload file!');
        }

        $captivePortalModel->path = $storage->url($path.'/'.$fileName);
        $captivePortalModel->save();

        // Download File on local disk
        self::cleanLocalCPFiles($captivePortalModel->device_group_id);
        $file = $storage->get($path.'/'.$fileName);
        $localStorage->put($path.'/'.$fileName, $file);

        // Unzip the file on local disk
        $zip = new \ZipArchive;
        if ($zip->open($localStorage->path($path.'/'.$fileName)) === true) {
            if (! $localStorage->exists($path.'/preview')) {
                $localStorage->makeDirectory($path.'/preview');
            }
            $zip->extractTo($localStorage->path($path.'/preview'));
            $zip->close();
        } else {
            throw new \Exception('Failed to open the zip file.');
        }

        // Upload extracted files to storage
        $files = $localStorage->allFiles($path);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && basename($file) !== 'arra-cp.zip') {
                $storage->put($file, $localStorage->get($file));
            }
        }

        self::cleanLocalCPFiles($captivePortalModel->device_group_id);
    }

    public static function deleteOldCPFiles(int $deviceGroupID): void
    {
        if (empty(self::$disk)) {
            self::initializeDisk();
        }
        $path = 'captive_portals/'.$deviceGroupID;
        $storage = Storage::disk(self::$disk);
        foreach ($storage->files($path, true) as $file) {
            $storage->delete($file);
        }
        $storage->deleteDirectory($path);
    }

    public static function cleanLocalCPFiles(int $deviceGroupID): void
    {
        $path = 'captive_portals/'.$deviceGroupID;
        $storage = Storage::disk('local');
        foreach ($storage->files($path, true) as $file) {
            $storage->delete($file);
        }
        $storage->deleteDirectory($path);
        $storage->deleteDirectory('captive_portals');
    }
}
