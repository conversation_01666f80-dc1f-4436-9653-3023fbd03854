<?php

namespace Arra\CaptivePortalsManagement;

use App\Models\DeviceGroup;
use Arra\CaptivePortalsManagement\Hooks\MenuEntry;
use Arra\CaptivePortalsManagement\Observers\DeviceGroupObserver;
use Illuminate\Foundation\Console\AboutCommand;
use Illuminate\Support\ServiceProvider;
use LibreNMS\Interfaces\Plugins\Hooks\MenuEntryHook;
use LibreNMS\Interfaces\Plugins\PluginManagerInterface;

class CaptivePortalsManagementProvider extends ServiceProvider
{
    /**
     * Bootstrap any package services.
     */
    public function boot(PluginManagerInterface $pluginManager): void
    {
        $pluginName = 'arra-captive-portals-management';

        // register hooks with LibreNMS (if any are desired)
        // if no hooks are defined, LibreNMS may delete the plugin from the ui
        // if you don't want any specific hooks, you can just register a settings hook
        // $pluginManager->publishHook($pluginName, MenuEntryHook::class, MenuEntry::class);

        if (! $pluginManager->pluginEnabled($pluginName)) {
            return; // if plugin is disabled, don't boot
        }

        AboutCommand::add('Arra Captive Portal Management', fn (): array => ['Version' => '1.0.0']);

        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
        $this->loadViewsFrom(__DIR__.'/../resources/views', $pluginName);
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');

        $this->bootObservers();
    }

    private function bootObservers(): void
    {
        DeviceGroup::observe(DeviceGroupObserver::class);
    }
}
