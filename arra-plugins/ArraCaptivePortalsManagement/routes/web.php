<?php

use Arra\CaptivePortalsManagement\Controllers\HomeController;
use Illuminate\Support\Facades\Route;

Route::get('/arra/captive-portals-management/get-cp-latest-theme', [HomeController::class, 'getCPThemeByMacAddress'])->name('arra-captive-portals-management.get-cp-latest-theme');
Route::get('/arra/captive-portals-management/get-cp-version', [HomeController::class, 'getCPVersion'])->name('arra-captive-portals-management.get-cp-version');
Route::get('/arra/captive-portals-management/get-s3-private-files', [HomeController::class, 'gets3PrivateFiles']);
Route::get('/arra/captive-portals-management/get-s3-public-files', [HomeController::class, 'gets3PublicFiles']);

Route::middleware(['web', 'auth'])->group(function (): void {
    Route::get('/arra/captive-portals-management', [HomeController::class, 'index'])->name('arra-captive-portals-management.index');
    Route::get('/arra/captive-portals-management/{captive_portal}/edit', [HomeController::class, 'edit'])->name('arra-captive-portals-management.edit');
    Route::put('/arra/captive-portals-management/{captive_portal}', [HomeController::class, 'update'])->name('arra-captive-portals-management.update');
    Route::delete('/arra/captive-portals-management/{captive_portal}', [HomeController::class, 'delete'])->name('arra-captive-portals-management.delete');
});
