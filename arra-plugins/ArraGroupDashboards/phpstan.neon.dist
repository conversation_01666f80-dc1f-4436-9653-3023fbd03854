includes:
    - vendor/larastan/larastan/extension.neon
    - vendor/nesbot/carbon/extension.neon

parameters:
    level: 5
    phpVersion:
        min: 80200
        max: 80499
    paths:
        - src/

    reportUnmatchedIgnoredErrors: false

    # Include LibreNMS autoloader to resolve dependencies
    bootstrapFiles:
        - ../../vendor/autoload.php
        - larastan-bootstrap.php

    # Scan additional directories for class discovery
    scanDirectories:
        - ../../app/Models
        - ../../LibreNMS
        - ../../includes
        - ../../arra-plugins

    # Ignore common Laravel/Eloquent magic method issues
    ignoreErrors:
        # Allow dynamic properties on our models
        - '#Access to an undefined property Arra\\GroupDashboards\\Models\\.*::\$.*#'
        # Ignore nullable coalesce on non-nullable expressions
        - '#Expression on left side of \?\? is not nullable#'
        # Ignore always true comparisons from PHPDoc types
        - '#Comparison operation ">" between int<1, max> and 0 is always true#'
        # Ignore relation existence issues when plugin is conditionally loaded
        - '#Relation .* is not found in App\\Models\\Dashboard model#'
