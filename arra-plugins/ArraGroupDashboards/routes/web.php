<?php

use Arra\GroupDashboards\Http\Controllers\EditController;
use Arra\GroupDashboards\Http\Controllers\IndexController;
use Arra\GroupDashboards\Http\Controllers\UpdateController;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'auth', 'allow-only-admin'])->group(function (): void {
    Route::get('arra/group-dashboards', IndexController::class)->name('arra.group-dashboards.index');
    Route::get('arra/group-dashboards/edit/{id}', EditController::class)->where('id', '[0-9]+')->name('arra.group-dashboards.edit');
    Route::put('arra/group-dashboards/{id}', UpdateController::class)->where('id', '[0-9]+')->name('arra.group-dashboards.update');
});
