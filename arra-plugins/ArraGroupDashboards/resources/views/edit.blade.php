@extends('layouts.librenmsv1')

@section('title', __('Edit Group Dashboards'))

@section('content')
    <div class="container">
        <div class="row">
            <form method="POST" role="form" action="{{route('arra.group-dashboards.update', $group->id)}}"
                  class="form-horizontal gd-form col-md-10 col-md-offset-1 col-sm-12">
                <legend>@lang('Add/Edit Dashboards for Group'): {{ $group->name }}</legend>
                {{ method_field('PUT') }}
                @csrf

                @include('arra-group-dashboards::form')

                <div class="form-group">
                    <div class="col-sm-9 col-sm-offset-3 col-md-10 col-sm-offset-2">
                        <button type="submit" class="btn btn-primary" id="save_bt">@lang('Save')</button>
                        <a type="button" class="btn btn-danger"
                           href="{{ route('arra.group-dashboards.index') }}">@lang('Cancel')</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('javascript')

@endsection

@section('css')

@endsection
