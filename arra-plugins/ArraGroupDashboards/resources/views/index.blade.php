@extends('layouts.librenmsv1')

@section('title', __('Group Dashboards'))

@section('content')
    <div class="container-fluid">
        <x-panel id="manage-group-dashboards-panel">
            <x-slot name="title">
                <i class="fa fa-bars fa-fw fa-lg" aria-hidden="true"></i> {{ __('Group Dashboards') }}
            </x-slot>
            <div class="table-responsive">
                <table id="group-dashboards-table" class="table table-condensed table-hover">
                    <thead>
                    <tr style="background: transparent !important;">
                        <th>@lang('Group')</th>
                        <th>@lang('Dashboards')</th>
                        <th>@lang('Actions')</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($groups as $group)
                        <tr id="row_{{ $group['id'] }}" style="background: transparent !important;">
                            <td>{{ $group['name'] }}</td>
                            <td>
                                @if( isset($groups_dashboards[$group['id']]) )
                                    @foreach($groups_dashboards[$group['id']] as $dashboard)
                                        <a href="{{route('overview')}}?dashboard={{$dashboard['id']}}" class="interface-upup">{{$dashboard['user']}}:{{$dashboard['name']}}</a>
                                        @if($loop->last) @else, @endif
                                    @endforeach
                                @endif
                            </td>

                            <td>
                                <a type="button" title="@lang('add/edit Dashboards')" class="btn btn-primary btn-sm" aria-label="@lang('Edit')"
                                   href="{{ route("arra.group-dashboards.edit", ['id' => $group['id']]) }}">
                                    <i class="fa fa-pencil" aria-hidden="true"></i></a>
{{--                                <button type="button" class="btn btn-danger btn-sm" title="@lang('delete Dashboards')" aria-label="@lang('Delete')"--}}
{{--                                        onclick="delete_gd('{{ $group['id'] }}', '{{ route('arra.group-dashboards.index') }}')">--}}
{{--                                    <i class="fa fa-trash" aria-hidden="true"></i></button>--}}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </x-panel>

    </div>
@endsection
@push('scripts')
    @if(session()->has('successMessage'))
        <script type="text/javascript">
            $( document ).ready(function() {
                toastr.success('{{ session()->get("successMessage") }}');
            });
        </script>
    @endif
@endpush

@section('css')
    <style>
        .table-responsive {
            padding-top: 16px
        }
    </style>
@endsection
