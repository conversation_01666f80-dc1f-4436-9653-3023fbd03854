{"name": "arra/group-dashboards", "description": "LibreNMS Arra Group Dashboards plugin", "keywords": ["librenms", "plugin", "arra"], "type": "package", "license": "MIT", "authors": [{"name": "Arra Networkds"}], "autoload": {"psr-4": {"Arra\\GroupDashboards\\": "src/"}, "files": ["./helpers.php"]}, "autoload-dev": {"psr-4": {"Arra\\GroupDashboards\\Tests\\": "tests"}}, "require": {"php": "^8.2", "librenms/plugin-interfaces": "^1.0"}, "suggest": {"illuminate/support": "for the Laravel integration", "illuminate/contracts": "for the Laravel integration"}, "require-dev": {"laravel/framework": "^12.10", "laravel/pint": "^1.13.7", "mockery/mockery": "^1.6", "pestphp/pest": "^3.0", "phpunit/phpunit": "^11.5.3", "rector/rector": "^2.0", "larastan/larastan": "^3.0"}, "extra": {"laravel": {"providers": ["Arra\\GroupDashboards\\GroupDashboardsProvider"], "aliases": {"ExamplePlugin": "Arra\\GroupDashboards\\Facade"}}}, "scripts": {"refactor": "rector", "test:refactor": "rector process --dry-run --", "test:types": "phpstan analyse --ansi --memory-limit=512M", "test:unit": "pest --colors=always", "test:lint": "pint --test", "test": ["@test:refactor", "@test:lint", "@test:types", "@test:unit"], "fix:lint": "pint --preset laravel", "fix:refactor": "rector", "fix": ["@fix:refactor", "@fix:lint"]}, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}