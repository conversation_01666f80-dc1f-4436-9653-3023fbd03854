<?php

namespace Arra\GroupDashboards\Models;

use App\Models\Dashboard;
use App\Models\DeviceGroup;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $device_group_id
 * @property int $dashboard_id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property-read Dashboard $dashboard
 * @property-read DeviceGroup $deviceGroup
 */
class ArraGroupDashboard extends Model
{
    public function dashboard(): BelongsTo
    {
        return $this->belongsTo(Dashboard::class, 'dashboard_id', 'dashboard_id');
    }

    public function deviceGroup(): BelongsTo
    {
        return $this->belongsTo(DeviceGroup::class, 'device_group_id', 'id');
    }
}
