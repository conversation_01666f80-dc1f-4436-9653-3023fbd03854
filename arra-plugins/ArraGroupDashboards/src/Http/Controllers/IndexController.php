<?php

namespace Arra\GroupDashboards\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\DeviceGroup;
use Arra\GroupDashboards\Models\ArraGroupDashboard;

class IndexController extends Controller
{
    public function __invoke()
    {
        $groupDashboardsArray = [];
        $groupDashboards = ArraGroupDashboard::whereHas('dashboard')->with('dashboard')->get();
        $counter = 0;
        foreach ($groupDashboards as $groupDashboard) {
            $groupDashboardsArray[$groupDashboard->device_group_id][$counter]['id'] = $groupDashboard->dashboard->dashboard_id;
            $groupDashboardsArray[$groupDashboard->device_group_id][$counter]['name'] = $groupDashboard->dashboard->dashboard_name;
            $groupDashboardsArray[$groupDashboard->device_group_id][$counter]['user'] = $groupDashboard->dashboard->user->username;
            $counter++;
        }

        return view('arra-group-dashboards::index', [
            'groups' => DeviceGroup::select('id', 'name')->get()->toArray(),
            'groups_dashboards' => $groupDashboardsArray,
        ]);
    }
}
