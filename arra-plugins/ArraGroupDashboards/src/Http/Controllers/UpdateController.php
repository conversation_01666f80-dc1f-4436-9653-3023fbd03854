<?php

namespace Arra\GroupDashboards\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\DeviceGroup;

class UpdateController extends Controller
{
    public function __invoke(int $deviceGroupId)
    {
        $group = DeviceGroup::findOrFail($deviceGroupId);
        $dashboards = request('dashboards', []);
        foreach ($dashboards as $key => $dashboard) {
            if (empty($dashboard)) {
                unset($dashboards[$key]);
            }
        }
        $group->dashboards()->sync($dashboards);

        return redirect()->route('arra.group-dashboards.index')->with('successMessage', 'Group dashboards updated successfully');
    }
}
