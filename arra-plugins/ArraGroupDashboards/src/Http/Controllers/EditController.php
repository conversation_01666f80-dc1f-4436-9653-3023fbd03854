<?php

namespace Arra\GroupDashboards\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Dashboard;
use App\Models\DeviceGroup;
use Illuminate\Database\Eloquent\Builder;

class EditController extends Controller
{
    public function __invoke(int $deviceGroupId)
    {
        $group = DeviceGroup::findOrFail($deviceGroupId);
        $superAdmins = config('app.app_superadmins');
        $superAdmins = explode(',', (string) $superAdmins);
        $adminDashboards = collect([]);

        // Get dashboards for this device group using direct join
        $groupDashboards = Dashboard::join('arra_group_dashboards', 'dashboards.dashboard_id', '=', 'arra_group_dashboards.dashboard_id')
            ->where('arra_group_dashboards.device_group_id', $deviceGroupId)
            ->with('user')
            ->get();
        $allDashboards = $groupDashboards;

        if (count($superAdmins) > 0) {
            $adminDashboards = Dashboard::whereHas('user', function (Builder $query) use ($superAdmins): void {
                $query->whereIn('username', $superAdmins);
            })->with('user')->get();
        }

        $groupDashboardsIds = $groupDashboards->pluck('dashboard_id')->toArray();

        if (count($adminDashboards) > 0) {
            $adminDashboardsFiltered = $adminDashboards->filter(fn ($dashboard) => ! in_array($dashboard->dashboard_id, $groupDashboardsIds));
            $allDashboards = $groupDashboards->merge($adminDashboardsFiltered);
        }

        return view('arra-group-dashboards::edit', [
            'group' => $group,
            'adminDashboards' => $adminDashboards,
            'groupDashboards' => $groupDashboards,
            'allDashboards' => $allDashboards,
            'groupDashboardsIds' => $groupDashboardsIds,
        ]);
    }
}
