<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('arra_group_dashboards', function (Blueprint $table) {
            $table->id();
            $table->integer('device_group_id')->index('device_group_id');
            $table->integer('dashboard_id')->index('dashboard_id');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('arra_group_dashboards');
    }
};
