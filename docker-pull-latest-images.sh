if [ ! -f ".env" ]
then
   echo ".env file is missing."
else
    source ./.env
fi

cd "${PATH_ENV}" || exit

echo
echo "Down all containers..."
docker compose --profile "*" down

echo
echo "Pull latest version of NMS image..."
docker compose --profile "*" pull librenms dispatcher dispatcher2 dispatcher3 syslogng snmptrapd librenms-cron librenms-queue

echo
echo "Remove older version of images..."
docker image ls -f "dangling=true" -q | xargs -r docker image rm

echo
echo "Start all containers..."
docker compose up -d
