{"private": true, "scripts": {"watch-production": "mix watch --production", "watch-prod": "npm run dev-production", "dev": "vite", "build": "vite build"}, "devDependencies": {"@tailwindcss/vite": "^4.0.14", "@vitejs/plugin-vue2": "^1.1.2", "autoprefixer": "^10.4.2", "axios": "^1.7.4", "bootstrap": "^4.5.3", "jquery": "^3.5.1", "laravel-vite-plugin": "^1.0", "lodash": "^4.17.21", "popper.js": "^1.16.1", "postcss": "^8.4.31", "resolve-url-loader": "^5.0.0", "sass": "^1.49.0", "sass-loader": "^12.4.0", "tailwindcss": "^4.0.13", "vite": "^6.3", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14"}, "dependencies": {"alpinejs": "^3.14.9", "babel-helper-vue-jsx-merge-props": "^2.0.3", "es6-object-assign": "^1.1.0", "v-tooltip": "^2.0.3", "vue": "^2.7.0", "vue-i18n": "^8.26.8", "vue-js-modal": "^2.0.1", "vue-js-toggle-button": "^1.3.3", "vue-multiselect": "^2.1.6", "vue-nav-tabs": "^0.5.7", "vue-select": "^3.16.0", "vuedraggable": "^2.24.3"}}