parameters:
	ignoreErrors:
		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Alert/AlertRules.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Alert/AlertRules.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/Alert/AlertRules.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/Alert/AlertRules.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Alert/AlertUtil.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/Alert/AlertUtil.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/Alert/RunAlerts.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/Alert/RunAlerts.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/Alert/RunAlerts.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/Alert/RunAlerts.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Alert/RunAlerts.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 6
			path: LibreNMS/Alert/RunAlerts.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/Billing.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/Billing.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 8
			path: LibreNMS/Billing.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/Billing.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/Billing.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/Component.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/Component.php

		-
			message: '''
				#^Call to deprecated function dbDeleteOrphans\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Device/Processor.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Device/Processor.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Device/Processor.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Device/Processor.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Device/Processor.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Device/YamlDiscovery.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Model.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/Model.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Model.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Model.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Model.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Model.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Modules/Isis.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/Modules/Isis.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Modules/Mempools.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Modules/Os.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/Modules/PrinterSupplies.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/Modules/PrinterSupplies.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: LibreNMS/Modules/PrinterSupplies.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Airos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: LibreNMS/OS/AirosAf60.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 9
			path: LibreNMS/OS/AirosAfLtu.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: LibreNMS/OS/Allied.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Allied.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/AllworxVoip.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Apc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/Apc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Apsoluteos.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/AsuswrtMerlin.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: LibreNMS/OS/AviatWtm.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Avocent.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Axos.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Barracudangfirewall.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/OS/Bats.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Beagleboard.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Boss.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Boss.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Boss.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Brother.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Ceraos.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ceraos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 10
			path: LibreNMS/OS/Ceraos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/Ciscosat.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ciscosb.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Comware.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/OS/Comware.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Coriant.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/DdWrt.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Deliberant.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Dhcpatriot.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Dlink.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/OS/Dlinkap.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Edgecos.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Edgecos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Edgeos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Edgeosolt.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Edgeswitch.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ekinops.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Enterasys.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: LibreNMS/OS/Epmp.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Epmp.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: LibreNMS/OS/Ericsson6600.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: LibreNMS/OS/EricssonTn.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ewc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 8
			path: LibreNMS/OS/Ewc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Fabos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Fortiap.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/Fortiextender.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Fortigate.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Fortios.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/FsGbn.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/FsSwitch.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Gaia.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/HarmonyEnhanced.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Helios.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/HiveosWireless.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 11
			path: LibreNMS/OS/HorizonQuantum.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ifotec.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ifotec.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/InfineraGroove.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ios.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 9
			path: LibreNMS/OS/Ios.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Iosxe.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: LibreNMS/OS/Iosxe.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Iosxr.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Jetdirect.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Junos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: LibreNMS/OS/Junos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 10
			path: LibreNMS/OS/Lcos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 11
			path: LibreNMS/OS/Lcoslx.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Lcossx.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: LibreNMS/OS/Mimosa.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/OS/Mimosa.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Netscaler.php

		-
			message: '''
				#^Call to deprecated function snmp_getnext\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Netsure.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/Nios.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/OS/Openwrt.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Panos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: LibreNMS/OS/Pepwave.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Pfsense.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 10
			path: LibreNMS/OS/Pmp.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Pmp.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/Pmp.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/OS/Powerconnect.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Procurve.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Quanta.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Quantastor.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Radlan.php

		-
			message: '''
				#^Call to deprecated function snmp_getnext\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Radlan.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/Riverbed.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Routeros.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/RuckuswirelessSz.php

		-
			message: '''
				#^Call to deprecated function snmp_getnext\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/RuckuswirelessSz.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Rutos2xx.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Scalance.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Schleifenbauer.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Schleifenbauer.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Screenos.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Secureplatform.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Sgos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Sgos.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Shared/Cisco.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Shared/Cisco.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Shared/Cisco.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Shared/Cisco.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: LibreNMS/OS/Shared/Cisco.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: LibreNMS/OS/Shared/Cisco.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Shared/Printer.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Shared/Unix.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/Shared/Unix.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Shared/Zyxel.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Shared/Zyxel.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Siklu.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Siteboss.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Siteboss550.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/SmOs.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: LibreNMS/OS/SmOs.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Smartax.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Svos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: LibreNMS/OS/Teldat.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Terra.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/ThreeCom.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Topvision.php

		-
			message: '''
				#^Call to deprecated function snmp_getnext\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Topvision.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Ucos.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 9
			path: LibreNMS/OS/Unifi.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Valere.php

		-
			message: '''
				#^Call to deprecated function snmp_getnext\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Vrp.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 8
			path: LibreNMS/OS/Vrp.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 9
			path: LibreNMS/OS/Vrp.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 8
			path: LibreNMS/OS/Vrp.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: LibreNMS/OS/Windows.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/WisBridge.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Xerox.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/XirrusAos.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Zynos.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: LibreNMS/OS/Zywall.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: LibreNMS/OS/Zyxelwlc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: html/data.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/common.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/common.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/common.php

		-
			message: '''
				#^Call to deprecated function dbDeleteOrphans\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/applications.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/applications.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/applications.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/dell-os10.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/dell-os10.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/dell-os10.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/firebrick.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/firebrick.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/vrp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/vrp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/bgp-peers/vrp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/bgp-peers/vrp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-mac-accounting.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-mac-accounting.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-mac-accounting.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-mac-accounting.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/cisco-otv.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-pw.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/cisco-pw.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-pw.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-pw.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/discovery/cisco-pw.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-qfp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-qfp.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-vrf-lite.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-vrf-lite.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-vrf-lite.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-vrf-lite.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/cisco-vrf-lite.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/cisco-vrf-lite.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/discovery-arp.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/discovery-protocols.inc.php

		-
			message: '''
				#^Call to deprecated function dbDeleteOrphans\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/discovery-protocols.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/discovery-protocols.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/discovery-protocols.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/discovery-protocols.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/discovery-protocols.inc.php

		-
			message: '''
				#^Call to deprecated function dbBulkInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/fdb-table.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/fdb-table/aos6.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/fdb-table/aos7.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/fdb-table/arubaos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/fdb-table/bridge.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/fdb-table/edgeswitch.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/fdb-table/ios.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/fdb-table/ios.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/fdb-table/vrp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/fdb-table/zynos.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/hr-device.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/hr-device.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/hr-device.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/hr-device.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/hr-device.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/hr-device.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/junose-atm-vp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/junose-atm-vp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/junose-atm-vp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/junose-atm-vp.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/loadbalancers/f5-gtm.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/loadbalancers/f5-gtm.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/loadbalancers/f5-ltm.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/loadbalancers/f5-ltm.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/mef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/mef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/mef.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/mef.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/mef.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/awplus.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/awplus.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/awplus.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/awplus.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ntp/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ports.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ports.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/ports.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/ports.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/ports/adva-fsp150cp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ports/airos-af-ltu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ports/brocade.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ports/cnmatrix.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ports/edgeosolt.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/ports/loop-telecom.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/ports/mlos-nsp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ports/slms.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/route.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/route.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/route.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/route.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/route.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/route.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/route.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/airflow/geist-watchdog.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/charge/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/charge/compas.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/charge/dsm.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/charge/eatonups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/charge/eltex-mes23xx.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/charge/linux.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/charge/netagent2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/charge/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/sensors/cisco-entity-sensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/cisco-entity-sensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/count/christie-projector.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/count/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/count/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/count/dhcpatriot.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/count/epson-projector.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/count/printer.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/count/sonicwall.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/count/webmon.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 26
			path: includes/discovery/sensors/current/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/current/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/current/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/current/commander-plus.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/current/compas.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/cyberpower.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/current/eatonups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/fs-net-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/current/gamatronicups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/ict-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/ict-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/ict-psu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/current/ipoman.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_getnext\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/liebert.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/current/liebert.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/current/linux.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/current/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/current/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/netagent2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/netonix.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 15
			path: includes/discovery/sensors/current/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/current/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/current/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/current/sitemonitor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/dbm/fabos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/dbm/fabos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 16
			path: includes/discovery/sensors/dbm/fs-nmu.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/entity-sensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/sensors/entity-sensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/entity-sensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/areca.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/areca.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/benuos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/dell.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/eltex-olt.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/equallogic.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/equallogic.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/f5.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/netonix.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/fanspeed/nos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/nos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/onefs.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/quanta.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/fanspeed/sgos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/fanspeed/supermicro.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/supermicro.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/xos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/fanspeed/xos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/frequency/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/frequency/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/frequency/eaton-ats.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/frequency/eaton-ats.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/frequency/eatonups.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/frequency/ipoman.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/frequency/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/frequency/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/discovery/sensors/frequency/netagent2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/frequency/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/frequency/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/frequency/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/frequency/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/frequency/rs.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 32
			path: includes/discovery/sensors/gw-eydfa.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/gw-eydfa.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/humidity/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/humidity/ipoman.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/humidity/liebert.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/humidity/liebert.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/humidity/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/discovery/sensors/humidity/minkelsrms.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/humidity/minkelsrms.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/humidity/pcoweb.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/humidity/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/humidity/sentry3.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/humidity/webmon.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/load/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/load/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/load/dhcpatriot.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/load/dhcpatriot.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/load/dsm.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/load/netagent2.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/load/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/netscaler.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/openbsd.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/ciscosb.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/dell-ups.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/dell.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/power/dsm.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/eatonups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/power/hpblmos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/hpblmos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/netonix.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/sensors/power/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/power/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/power/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/smartax.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/power/smartax.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power/xos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/power_consumed/ipoman.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/adva-aos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 18
			path: includes/discovery/sensors/pre-cache/adva_fsp150.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 19
			path: includes/discovery/sensors/pre-cache/adva_fsp3kr7.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/aos-emu2.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/aos-emu2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/pre-cache/arris-c3.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/pre-cache/arris-c4.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/avtech.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/ciscoepc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/ciscosb.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/dell-powervault.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/eltex-mes21xx.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/eltex-mes23xx.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/pre-cache/enlogic-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/exos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/geist-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/ifotec.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/ifotec.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/pre-cache/ipoman.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/linux.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/luminato.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/nokia-isam.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/nokia-isam.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/nokia-isam.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/pbn.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/poweralert.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/raisecom-ros.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/raisecom.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/pre-cache/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 17
			path: includes/discovery/sensors/pre-cache/schleifenbauer.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 9
			path: includes/discovery/sensors/pre-cache/sentry4.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/serverscheck.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/terra-sdi410c.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/terra-sdi480.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/tpdin.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/pre-cache/websensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/pre-cache/wipipe.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/rittal-cmc-iii-sensors.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/rittal-cmc-iii-sensors.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/rpigpiomonitor.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 11
			path: includes/discovery/sensors/runtime/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/runtime/compas.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/runtime/dell-ups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/runtime/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/signal/cambium.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/signal/canopy.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/airos-af-ltu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/state/airos-af60.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/state/aos-emu2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/aos6.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/aos6.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/sensors/state/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/arubaos-cx.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/axiscam.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/boss.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/commander-plus.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/compas.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/ctm.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/dell.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/dell.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/dhcpatriot.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/eltek-webpower.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/equallogic.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/state/equallogic.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/eurostor.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/extendair.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/state/fs-nmu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/hp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/hp.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/hpblmos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/hpblmos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/ict-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/innovaphone.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/juniper-mss.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/loop-telecom.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 13
			path: includes/discovery/sensors/state/netagent2.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/nokia-isam.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/pcoweb.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/powervault.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/printer.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/procurve.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/quanta.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/serveriron.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/sitemonitor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/state/sm-os.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/state/voss.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/state/wipipe.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/aen.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/akcp.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/aos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/aos6.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 8
			path: includes/discovery/sensors/temperature/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/temperature/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/areca.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/areca.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/temperature/arris-c4.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/ats.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/axiscam.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/beagleboard.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/benuos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/binos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/binox.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/boss.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 8
			path: includes/discovery/sensors/temperature/calix.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/temperature/calix.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/temperature/ciscowlc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/commander-plus.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/compas.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/comware.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/dell.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/dhcpatriot.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/dnos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/dnos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/eltex-olt.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/equallogic.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/equallogic.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/ericsson-ml.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/extendair.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/f5.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/temperature/ftos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/geist-watchdog.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/geist-watchdog.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/hpblmos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/hpblmos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/hytera.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/hytera.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/ibmnos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/ies.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/ies.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/temperature/ipoman.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/junose.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/junose.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/lantronix-slc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/liebert.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/liebert.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/temperature/linux.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/linux.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/microsemipdsine.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/mimosa.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/discovery/sensors/temperature/minkelsrms.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/minkelsrms.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/netagent2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/temperature/netapp.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/netapp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/netonix.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/nokia-isam.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/nos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/nos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/onefs.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/papouch-tme.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/pcoweb.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/powerconnect.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/powerconnect.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/powerconnect.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/quanta.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/temperature/raisecom.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/saf.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/sentry3.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/sentry4.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/seos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/seos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/temperature/serveriron.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/temperature/sgos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/siklu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/temperature/sitemonitor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/smartax.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/sub10.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/temperature/sub10.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/temperature/supermicro.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/supermicro.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/terra.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/sensors/temperature/voss.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/voss.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/webmon.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/websensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/websensor.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/xos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/temperature/zxdsl.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/sensors/voltage/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/voltage/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/voltage/apc.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/areca.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/areca.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/benuos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/ceraos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/commander-plus.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/compas.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/cyberpower.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/dell.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/discovery/sensors/voltage/dsm.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/voltage/eaton-ats.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/eaton-ats.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/voltage/eatonups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/fs-net-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/voltage/gamatronicups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/geist-watchdog.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/hytera.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/hytera.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/ict-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/voltage/ict-psu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/voltage/ipoman.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 8
			path: includes/discovery/sensors/voltage/linux.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/linux.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/sensors/voltage/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/voltage/mgeups.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/microsemipdsine.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 15
			path: includes/discovery/sensors/voltage/netagent2.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/netonix.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/onefs.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/procurve.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/sensors/voltage/raritan-pdu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/voltage/rfc1628.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/saf.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/siklu.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/sensors/voltage/sitemonitor.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/sensors/voltage/terra.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ucd-diskio.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ucd-diskio.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ucd-diskio.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ucd-diskio.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/ucd-diskio.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/vlans.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/aos6.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/aos6.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/aos7.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/aos7.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/aos7.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/aos7.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/boss.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/boss.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/discovery/vlans/boss.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/cisco-vtp.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/cisco-vtp.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/cisco-vtp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/vlans/cisco-vtp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/vlans/cisco-vtp.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/jetstream.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/jetstream.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/jetstream.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/vlans/jetstream.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/junos.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/junos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/junos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 9
			path: includes/discovery/vlans/junos.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vlans/q-bridge-mib.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_twopart_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/discovery/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbQuery\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#building\-queries$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/functions.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 9
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 36
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbSyncRelationship\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/html/api_functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/application/proxmox.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/application/proxmox.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/common/alerts.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/dev-groups-overview-data.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-details.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-notes.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-notes.inc.php

		-
			message: '''
				#^Call to deprecated function dbBulkInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbSyncRelationship\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-templates.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/alert-templates.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-templates.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-transports.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-transports.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/alert-transports.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/alert-transports.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/create-alert-item.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/create-alert-item.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/html/forms/delete-alert-rule.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/delete-alert-rule.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/delete-alert-template.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/forms/delete-alert-transport.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/delete-cluster-poller.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/delete-cluster-poller.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/delete-customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/delete-poller.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/delete-poller.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/forms/delete-transport-group.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/get-host-dependencies.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/html/forms/get-host-dependencies.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/mempool-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/notifications.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/notifications.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/forms/notifications.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/parse-alert-rule.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/html/forms/parse-alert-rule.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/forms/parse-alert-template.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/parse-alert-template.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/parse-alert-template.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/parse-customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/parse-poller-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/poller-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/poller-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/processor-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/rediscover-device.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/routing-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/html/forms/schedule-maintenance.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/forms/schedule-maintenance.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/schedule-maintenance.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/schedule-maintenance.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/schedule-maintenance.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/sensor-alert-reset.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/sensor-alert-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/sensor-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/show-alert-transport.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/show-transport-group.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/show-transport-group.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/sql-from-alert-rules.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/storage-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/token-item-create.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/token-item-disable.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/token-item-remove.inc.php

		-
			message: '''
				#^Call to deprecated function dbBulkInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/transport-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/transport-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/transport-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/transport-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/transport-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/transport-groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/update-alert-rule.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/update-ifalias.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/update-ports.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/wireless-sensor-alert-reset.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/forms/wireless-sensor-alert-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/forms/wireless-sensor-update.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/atmvp/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/bgp/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/bill/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/bill/bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/bill/historicbits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/cefswitching/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/customer/bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/customer/bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/diskio_common.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/processor.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_IcmpEcho.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_IcmpTimeStamp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_icmpAppl.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_icmpjitter_iajitter.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_icmpjitter_jitter.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_icmpjitter_latency.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_icmpjitter_lost.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_icmpjitter_oos.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_jitter-icpif.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_jitter-latency.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_jitter-loss.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_jitter-lost.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_jitter-mos.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/sla_jitter.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/storage.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/toner.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/device/wireless-sensor.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/diskio/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/global/bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/global/processor_separate.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/global/processor_stack.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/ipsectunnel/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/location/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/location/bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/macaccounting/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/mempool/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/multiport/bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/graphs/multiport/bits_duo.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/multiport/bits_separate.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/graphs/multiport/bits_trio.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/graphs/munin/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/munin/graph.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/netscalervsvr/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/port/mac_acc_total.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/graphs/port/mac_acc_total.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/port/mac_acc_total.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/port/pagp_bits.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/processor/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/rserver/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/service/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/storage/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/toner/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/vserver/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/graphs/wireless/auth.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/devices.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/devices.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/devices.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/locations.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/locations.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/transport_groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/transport_groups.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/transports.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/list/transports.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/modal/alert_rule_list.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/modal/new_bill.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/modal/new_bill.inc.php

		-
			message: '''
				#^Call to deprecated function gen_snmpwalk_cmd\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/output/capture.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/output/query.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/addhost.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/addsrv.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/apps/proxmox.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 12
			path: includes/html/pages/bill.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/bill.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/bill.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 8
			path: includes/html/pages/bill/actions.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/bill/actions.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/bill/actions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/bill/edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/bill/history.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 8
			path: includes/html/pages/bill/transfer.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/bills.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/delhost.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/device/accesspoints.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/device/edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/edit/apps.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/edit/device.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/device/edit/device.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/edit/sensors-common.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/edit/snmp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/entphysical.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/entphysical.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/entphysical.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/device/graphs.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/graphs/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/health/diskio.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/health/processor.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/health/storage.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/hrdevice.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/hrdevice.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/hrdevice.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/loadbalancer/ace_rservers.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/loadbalancer/ace_vservers.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/device/loadbalancer/netscaler_vsvr.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/mef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/munin.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/overview/eventlog.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/overview/ports.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/overview/processors.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/overview/storage.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/overview/syslog.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/packages.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/pages/device/port.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/port.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/port/events.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/port/junose-atm-vp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/port/macaccounting.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/device/port/macaccounting.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/port/macaccounting.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/port/pagp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/device/port/vlans.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/processes.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/pseudowires.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/pseudowires.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/routing/bgp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/device/routing/bgp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/device/routing/bgp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/device/routing/cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/routing/cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/routing/ipsec_tunnels.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/pages/device/routing/mpls.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/html/pages/device/routing/mpls.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/html/pages/device/routing/ospf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/routing/ospf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/pages/device/routing/ospf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/routing/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/slas.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/slas.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/wireless.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/device/wireless.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/devices.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/edituser.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/html/pages/edituser.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/edituser.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/edituser.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/iftype.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/packages.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/peering/as-selection.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/pseudowires.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/pseudowires.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/routing/bgp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/routing/bgp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/routing/cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/routing/cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/routing/mpls-path-map.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/routing/mpls-path-map.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/pages/routing/mpls.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/html/pages/routing/mpls.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/pages/routing/ospf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/routing/ospf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/html/pages/routing/vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/arp.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/arp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/fdb.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/fdb.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/ipv4.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/ipv4.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/ipv6.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/ipv6.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/mac.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/mac.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/packages.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/search/packages.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/search/packages.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/pages/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchColumn\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/pages/wireless.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/html/print-alert-rules.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/html/print-alert-rules.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/print-alert-rules.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/print-alert-transports.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/print-customoid.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/print-customoid.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/print-graph-alerts.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/print-graph-alerts.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/print-vrf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/reports/alert-log.pdf.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/reports/ports.csv.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/address-search.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/address-search.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/address-search.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/alertlog-stats.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/alertlog-stats.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/alertlog.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/alertlog.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/alertlog.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/html/table/alerts.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/alerts.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/alerts.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/as-selection.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/as-selection.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/bills.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/bills.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/eventlog.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/eventlog.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/ix-list.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/ix-list.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/html/table/ix-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/mempool-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/mempool-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/poll-log.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/poll-log.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/processor-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/processor-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/routing-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/routing-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/sensors-common.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/sensors-common.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/sensors-common.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/storage-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/storage-edit.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/toner.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/html/table/toner.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/apache.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/asterisk.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/bind.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/bird2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/dhcp-stats.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/entropy.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/exim-stats.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/freeradius.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/freeswitch.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/icecast.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/mailscanner.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/mysql.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/nfs-server.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/nfs-stats.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/nfs-v3-stats.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/nginx.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/nvidia.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/opengridscheduler.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/opensips.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/os-updates.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/pi-hole.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/applications/postfix.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/postgres.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/powerdns-dnsdist.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/powerdns-recursor.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/applications/proxmox.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/applications/proxmox.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/applications/proxmox.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/proxmox.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/rrdcached.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/sdfsinfo.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/shoutcast.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/unbound.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/ups-nut.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_walk\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/ups-nut.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/applications/voip-monitor.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 12
			path: includes/polling/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 9
			path: includes/polling/bgp-peers.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/polling/cipsec-tunnels.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/cipsec-tunnels.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/cipsec-tunnels.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/cipsec-tunnels.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/cipsec-tunnels.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/cipsec-tunnels.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-loadbalancer.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-loadbalancer.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-loadbalancer.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-loadbalancer.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-serverfarms.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-serverfarms.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-serverfarms.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ace-serverfarms.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/cisco-cef.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-ipsec-flow-monitor.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-mac-accounting.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-mac-accounting.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/polling/cisco-otv.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/cisco-qfp.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-remote-access-monitor.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/cisco-vpdn.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/customoid.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/entity-state.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/functions.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/functions.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/functions.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/functions.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/hr-mib.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ipSystemStats.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ipmi.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ipmi.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/junose-atm-vp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 8
			path: includes/polling/junose-atm-vp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/polling/loadbalancers/f5-gtm.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 7
			path: includes/polling/loadbalancers/f5-ltm-currconns.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 23
			path: includes/polling/loadbalancers/f5-ltm.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/loadbalancers/f5-ltm.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/mef.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/mef.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/mef.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/netscaler-vsvr.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/netscaler-vsvr.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/netscaler-vsvr.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/netscaler-vsvr.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/netscaler-vsvr.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ntp/awplus.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_array_num\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ntp/cisco.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/ports.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 5
			path: includes/polling/ports.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 4
			path: includes/polling/ports.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 22
			path: includes/polling/ports.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/exalink-fusion.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/f5.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/os/adva-fsp150cp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/airos-af-ltu.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/airos-af.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/ciena-sds.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/os/cmm.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/cnmatrix.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/os/cxr-ts.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/edgeosolt.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/fabos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/ports/os/horizon-quantum.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_multi_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/os/infinera-groove.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/junos.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/os/loop-telecom.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/os/mlos-nsp.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/nokia-isam.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_group\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/ports/os/procera.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ports/os/slms.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/sensors.inc.php

		-
			message: '''
				#^Call to deprecated function dbGenPlaceholders\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/sensors.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/sensors/load/dhcpatriot.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/sensors/load/dhcpatriot.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/sensors/pre-cache/aos-emu2.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/sensors/pre-cache/geist-watchdog.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/sensors/temperature/cambium.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ucd-diskio.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ucd-diskio.inc.php

		-
			message: '''
				#^Call to deprecated function snmp_get_multi\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ucd-mib.inc.php

		-
			message: '''
				#^Call to deprecated function snmpwalk_cache_oid\(\)\:
				Please use SnmpQuery instead$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/ucd-mib.inc.php

		-
			message: '''
				#^Call to deprecated function dbBulkInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/unix-agent.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/unix-agent.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/unix-agent.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 3
			path: includes/polling/unix-agent.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRow\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/unix-agent/hddtemp.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/polling/unix-agent/hddtemp.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/unix-agent/munin-plugins.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/polling/unix-agent/munin-plugins.inc.php

		-
			message: '''
				#^Call to deprecated function dbDelete\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#deleting\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchRows\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 2
			path: includes/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbUpdate\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/services.inc.php

		-
			message: '''
				#^Call to deprecated function dbFetchCell\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent$#
			'''
			identifier: function.deprecated
			count: 6
			path: includes/syslog.php

		-
			message: '''
				#^Call to deprecated function dbInsert\(\)\:
				Please use Eloquent instead; https\://laravel\.com/docs/eloquent\#inserting\-and\-updating\-models$#
			'''
			identifier: function.deprecated
			count: 1
			path: includes/syslog.php
