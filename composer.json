{"name": "librenms/librenms", "type": "project", "description": "A fully featured network monitoring system that provides a wealth of features and device support.", "keywords": ["network", "monitoring", "discovery", "alerting", "billing", "snmp", "distributed"], "homepage": "https://www.librenms.org/", "license": "GPL-3.0-or-later", "repositories": [{"type": "path", "url": "arra-plugins/ArraCaptivePortalsManagement", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraMeshPlanner", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraBilling", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraGroupSettings", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraMeshDynamics", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraSetDeviceGroup", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraGroupDashboards", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraDeviceCmds", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraImportDevices", "options": {"symlink": true}}, {"type": "path", "url": "arra-plugins/ArraDeviceGroupAssignment", "options": {"symlink": true}}], "require": {"php": "^8.2", "ext-curl": "*", "ext-gd": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-zip": "*", "ext-zlib": "*", "amenadiel/jpgraph": "^4", "arra/billing": "@dev", "arra/captive-portals-management": "@dev", "arra/device-cmds": "@dev", "arra/device-group-assignment": "@dev", "arra/group-dashboards": "@dev", "arra/group-settings": "@dev", "arra/import-devices": "@dev", "arra/mesh-dynamics": "@dev", "arra/mesh-planner": "@dev", "arra/set-device-group": "@dev", "clue/socket-raw": "^1.4", "dapphp/radius": "^3.0", "doctrine/dbal": "^3.5", "easybook/geshi": "^1.0.8", "enshrined/svg-sanitize": "^0.21.0", "fico7489/laravel-pivot": "^3.0", "influxdata/influxdb-client-php": "^3.8", "influxdb/influxdb-php": "^1.15", "justinrainbow/json-schema": "^6.4", "laravel-notification-channels/webpush": "^10.0", "laravel/framework": "^12.10", "laravel/tinker": "^2.10.1", "laravel/ui": "^4.6", "league/flysystem-aws-s3-v3": "^3.0", "librenms/laravel-vue-i18n-generator": "dev-master", "librenms/plugin-interfaces": "^1.0", "mews/purifier": "^3.4", "nesbot/carbon": "^3.8", "nunomaduro/laravel-console-summary": "^1.13", "paragonie/sodium_compat": "^1.9", "pear/console_color2": "^0.1", "pear/console_table": "^1.3", "pear/net_dns2": "^1.5", "php-amqplib/php-amqplib": "^3.1", "phpmailer/phpmailer": "~6.0", "predis/predis": "^3.0", "socialiteproviders/manager": "^4.8", "spatie/laravel-ignition": "^2.9", "spatie/laravel-permission": "^6.16", "symfony/yaml": "^7.2", "tecnickcom/tcpdf": "^6.4", "tightenco/ziggy": "^2.0", "vladimir-yuldashev/laravel-queue-rabbitmq": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "barryvdh/laravel-ide-helper": "^3.5", "composer/composer": "^2.8", "fakerphp/faker": "^1.23", "friendsofphp/php-cs-fixer": "^3.66", "larastan/larastan": "^3.1", "laravel/dusk": "^8.2.2", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "php-parallel-lint/php-parallel-lint": "^1.1", "phpstan/phpstan-deprecation-rules": "^2.0", "phpstan/phpstan-mockery": "^2.0", "phpunit/phpunit": "^11.5.3"}, "suggest": {"ext-gmp": "Used for browser push notifications", "ext-ldap": "*", "ext-memcached": "Required if you utilize wrapper based distributed polling", "ext-mysqlnd": "*", "ext-posix": "Allows for additional validation tests"}, "config": {"discard-changes": true, "optimize-autoloader": true, "platform-check": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": false}}, "extra": {"laravel": {"dont-discover": ["nunomaduro/laravel-console-summary", "facade/ignition", "socialiteproviders/manager"]}}, "autoload": {"psr-4": {"App\\": "app", "LibreNMS\\": "LibreNMS", "LibreNMS\\Plugins\\": "html/plugins", "LibreNMS\\Tests\\": "tests", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "classmap": ["vendor/dapphp/radius"], "files": ["includes/helpers.php"], "exclude-from-classmap": ["/vendor/laravel/laravel/database/", "/vendor/laravel/laravel/app/", "/html/plugins"]}, "scripts": {"pre-install-cmd": "LibreNMS\\ComposerHelper::preInstall", "post-install-cmd": ["LibreNMS\\ComposerHelper::postInstall", "Illuminate\\Foundation\\ComposerScripts::postInstall", "@php artisan vue-i18n:generate --multi-locales --format=umd", "@php artisan view:cache", "@php artisan optimize", "@php artisan config:clear", "@python-requirements"], "pre-update-cmd": "LibreNMS\\ComposerHelper::preUpdate", "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": "LibreNMS\\ComposerHelper::postRootPackageInstall", "post-create-project-cmd": ["@php artisan key:generate --ansi"], "python-requirements": ["scripts/dynamic_check_requirements.py || pip3 install --user -r requirements.txt || :"]}, "support": {"issues": "https://github.com/librenms/librenms/issues/", "forum": "https://community.librenms.org/", "chat": "https://discord.gg/librenms", "source": "https://github.com/librenms/librenms/", "docs": "https://docs.librenms.org/"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/librenms"}], "minimum-stability": "stable", "prefer-stable": true}