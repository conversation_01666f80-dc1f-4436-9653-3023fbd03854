#!/bin/bash

#set -xu
bold=$(tput bold)
normal=$(tput sgr0)
ALL_ENV_SETS=true
available_envs=( "local" "development" "staging")

if [ ! -f ".env" ]
then
    if [ -z "$1" ]
    then
        echo "Please provide an environment as an argument."
        echo "Available environments are: $(printf "'%s' " "${available_envs[@]}")"
        echo "For example you can type: ./init_project.sh development"
        exit 1
    fi

    # shellcheck disable=SC2199
    if [[ ${available_envs[@]} =~ $1 ]]
    then
        APP_ENV=$1
        PATH_ENV="environments/$1"
        echo "APP_ENV=$1" > .env
        echo "PATH_ENV=$PATH_ENV" >> .env
    else
        echo "You typed an invalid environment."
        echo "Available environments are: $(printf "'%s' " "${available_envs[@]}")"
        echo "For example you can type: ./init_project.sh development"
        exit 1
    fi
else
    source ./.env
fi

if [ ! -f "$PATH_ENV/.env" ]
then
    echo -e "Creating environment file by copying from template $PATH_ENV/.env.example ..."
    cp $PATH_ENV/.env.example $PATH_ENV/.env
    ALL_ENV_SETS=false
fi

if [ ! -f "$PATH_ENV/librenms/librenms.env" ]
then
    echo -e "Creating librenms environment file by copying from template $PATH_ENV/librenms/librenms.env.example ..."
    cp $PATH_ENV/librenms/librenms.env.example $PATH_ENV/librenms/librenms.env
    ALL_ENV_SETS=false
fi

if [ ! -f "$PATH_ENV/librenms/msmtpd.env" ]
then
    echo -e "Creating msmtpd environment file by copying from template $PATH_ENV/librenms/msmtpd.env.example ..."
    cp $PATH_ENV/librenms/msmtpd.env.example $PATH_ENV/librenms/msmtpd.env
    ALL_ENV_SETS=false
fi

if [ $ALL_ENV_SETS = false ]
then
    echo -e "Please set all environment variables in ${bold}$PATH_ENV/.env${normal}, ${bold}$PATH_ENV/librenms/librenms.env${normal} and ${bold}$PATH_ENV/librenms/msmtpd.env${normal} !\n${bold}Then run this script again!"
    exit 1
fi

bash $PATH_ENV/scripts/init-project.sh

