function getVoucherTokenFromLocalstorage(){
	let name = 'arranetworks_voucher_token';
	try {
		let voucherToken = localStorage.getItem(name);

		if ( voucherToken ){
			return voucherToken;
		}
		return null;
	} catch(e) {
		return null;
    }
}

function setVoucherTokenInLocalstorage(){

	let name = 'arranetworks_voucher_token';
	let voucherToken = $("#voucher_token").val();
	try {
		localStorage.removeItem(name);
		localStorage.setItem(name, voucherToken);
	} catch(e) {
		return null;
    }
}

function getVoucherFromLocalstorage(){
	let name = 'arranetworks_voucher';
	try {
		let voucherToken = localStorage.getItem(name);

		if(voucherToken){
			return voucherToken;
		}
		return null;
	} catch(e) {
		return null;
    }
}

function setVoucherInLocalstorage(voucher_code){

	let name = 'arranetworks_voucher';
	try {
		localStorage.removeItem(name);
		localStorage.setItem(name, voucher_code);
	} catch(e) {
		return null;
    }
}

function changeVoucherStatus(voucherCode){
	// keep last voucher
	setVoucherInLocalstorage(voucherCode);

	$.get(CHECKOUT_URL + '/index.php?m=arranetworkscore&action=change_voucher_status&voucher='+ voucherCode, function(data){
		if(data){
			//console.log(data);
		}
	});
}

function setDevModeInLocalstorage(val){
	let name = 'arranetworks_dev_mode';
	try {
		if (val) {
			localStorage.setItem(name, val);
		} else {
			localStorage.removeItem(name);
		}
	} catch(e) {
		return null;
	}
}

function getDevModeFromLocalstorage(){
	let name = 'arranetworks_dev_mode';
	try {
		let devMode = localStorage.getItem(name);
		return !!devMode;

	} catch(e) {
		return false;
    }
}
