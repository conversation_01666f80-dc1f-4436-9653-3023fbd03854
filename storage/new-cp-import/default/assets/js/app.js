var CHECKOUT_URL = '';
var BUSINESS_MODEL_NAME = '';
var BUSINESS_MODEL_TYPE = '';
var intervalTimerId = null;

function debugLog(message, err) {
    if (devMode) {
        if (err) {
            console.log(message, err);
        } else {
            console.log(message);
        }
    }
}
function clearErrors() {
    $('#error_message').html('');
}

function showErrorMessage(message) {
    if (Array.isArray(message)) {
        message = message.join('<br/>')
    }
    $('#error_message').html(message);
}
function showLoading() {
    $('#loading').removeClass('d-none');
}

function hideLoading() {
    $('#loading').addClass('d-none');
}

async function init() {
    if (checkValidParamsInUrl()) {
        intervalTimerId = window.setInterval(async function () {
            await main(true);
        }, 10000);
        await main(false);
    }
    hideLoading();
}
async function main(reload) {
    if (checkNodeHashVar) {
        try {
            let isSynchronized = await checkNodeHash();
            if (!isSynchronized) {
                $('#check-router-hash').removeClass('d-none');
                hideLoading();
                debugLog('Node Not Synced');
                return ;
            } else {
                if (reload) {
                    window.location.reload();
                }
                $('#check-router-hash').addClass('d-none');
                clearErrors();
                debugLog('Node Synced');
                window.clearInterval( intervalTimerId );
            }
        } catch (e) {
            debugLog('catch sync error', e);
            hideLoading();
            showErrorMessage("SYSTEM NOTIFICATION:<br>The node might be out of sync. Please contact the administrator.")
            return;
        }
    } else {
        debugLog('Check Node Hash is disabled');
        window.clearInterval( intervalTimerId );
    }

    let connect = sConnect({});
    connect.init();
    let checkBusinessModel = await getBusinessModel();
    if (checkBusinessModel) {
        await getProducts();
        await autocompleteVoucherInput();
        await checkSubscriptionKeyInURL();
        await checkRouterLoginInURL();
        if (BUSINESS_MODEL_NAME === 'monthly') {
            if (BUSINESS_MODEL_TYPE === 'user') {
                $('#subtitle-get-user-subscription').removeClass('d-none');
                $('#promo_user_subscription').parent().removeClass('d-none');
            } else {
                $('#subtitle-get-node-subscription').removeClass('d-none');
                $('#promo_node_subscription').parent().removeClass('d-none');
            }
            $('#products_list').parent().removeClass('d-none');
        }
    }
}

function checkNodeHash() {
    debugLog('call function checkNodeHash')
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "/arra-gc.hash",
            type: 'GET',
            data: {}
        })
        .done(function (nodeHASH) {
            if (!nodeHASH || nodeHASH.length === 0) {
                showErrorMessage("SYSTEM NOTIFICATION:<br>The node might be out of sync. Please contact the administrator.")
                reject();
            }
            let mac_address = urlParams.get('mac');
            $.ajax({
                url: NMS_URL + NMS_GET_GROUP_CONFIG_VERSION_ENDPOINT +'?mac=' + mac_address + '&rev=1',
                type: 'GET',
                data: {}
            })
            .done(function (groupConfigHASH) {
                debugLog('groupConfigHASH',groupConfigHASH);
                debugLog('nodeHASH',nodeHASH);
                if (groupConfigHASH == nodeHASH) {
                    resolve(true)
                } else {
                    resolve(false)
                }
            })
            .fail(function(err) {
                debugLog('Fail get-group-config-version: ',err);
                reject();
            })
        })
        .fail(function(err) {
            debugLog('Fail get ajax arra-gc.hash: ',err);
            reject();
        })
    });
}

function checkInternetConnection() {
    let xhr = new XMLHttpRequest();
    let url = RADIUSDESK_URL + "/wifi/check.php";
    let r = Math.round(Math.random() * 10000);
    xhr.open('HEAD', url + '?subins=' + r, false);
    try {
        xhr.send();
        return xhr.status >= 200 && xhr.status < 304;
    } catch (e) {
        return false;
    }
}

function checkValidParamsInUrl() {

    if (!urlParams.has('nasid')) {
        showErrorMessage('Oops! We could not detect NasId!');
        return false;
    }
    if (!urlParams.has('uamip')) {
        showErrorMessage('Oops! We could not detect the IP of this node!');
        return false;
    }
    if (!urlParams.has('uamport')) {
        showErrorMessage('Oops! We could not detect the PORT of this node!');
        return false;
    }
    if (!urlParams.has('mac')) {
        showErrorMessage('Oops! We could not detect the MAC Address of this node!');
        return false;
    }
    if (!urlParams.has('client_mac')) {
        showErrorMessage('Oops! We could not detect the MAC Address of this device!');
        return false;
    }
    return true;
}

function getBusinessModel() {
    debugLog('call function getBusinessModel');
    return new Promise((resolve, reject) => {
        if (!NMS_URL || !NMS_GET_GROUP_SETTINGS_ENDPOINT) {
            showErrorMessage('Config file for this CP it is not set correctly! Please contact the administrator!');
            resolve(false);
        }
        $.ajax({
            url: NMS_URL + NMS_GET_GROUP_SETTINGS_ENDPOINT + '?mac='+urlParams.get('mac') ,
            type: 'GET',
            data: {}
        })
        .done(function (response) {
            debugLog('Response Ajax getBusinessModel:', response);
            if (!response.success) {
                showErrorMessage(response.message);
                resolve(false);
            }

            if (!response.data || !response.data.products_type) {
                showErrorMessage('Something went wrong! Please try again later!');
                resolve(false);
            }

            if (response.data.products_type !== 'open-access' && !response.data.billing_url) {
                showErrorMessage('Something went wrong! Please try again later!');
                resolve(false);
            }

            if (response.data.products_type === 'open-access') {
                debugLog('Business Model is Open Access!', response);
                successLogin();
                resolve(false);
            }

            CHECKOUT_URL = response.data.billing_url;
            BUSINESS_MODEL_NAME = response.data.products_type;
            $('#business_model_name').val(response.data.products_type);
            debugLog('Business Model is '+response.data.products_type);
            if (response.data.products_type === 'monthly') {
                if (response.data.monthly_type && response.data.monthly_type === 1) {
                    debugLog('Business Model is '+response.data.products_type+' and type is user');
                    BUSINESS_MODEL_TYPE = 'user';
                    $('#business_model_type').val('user');
                } else {
                    debugLog('Business Model is '+response.data.products_type+' and type is router');
                    BUSINESS_MODEL_TYPE = 'router';
                    $('#business_model_type').val('router');
                }
            }
            resolve(true);
        })
        .fail(function (error) {
            debugLog('Get Billing Url Err: ', error);
            if (error.status === 422) {
                showErrorMessage(error.responseJSON.message || 'Something went wrong! Please try again later!');
            } else {
                showErrorMessage('Something went wrong! Please try again later!');
            }
            resolve(false);
        });
    })
}

function getProducts() {
    debugLog('call function getProducts');
    return new Promise((resolve, reject) => {
        const voucherToken = getVoucherTokenFromLocalstorage() || '';

        $.get(CHECKOUT_URL + '/index.php?m=arranetworkscore&action=get_groups_products&vt=' + voucherToken, function (data) {
            if (!data.success || !data.response || !data.response.products || !data.response.gateways || !data.response.business_model) {
                showErrorMessage('Oops! Something went wrong when get products. Please try again later!');
                return;
            }

            if (data.response.products.length === 0) {
                showErrorMessage('Oops! No products found. Please contact the administrator!');
                return;
            }

            if (data.response.gateways.length === 0) {
                showErrorMessage('Oops! No payment gateways found. Please contact the administrator!');
                return;
            }

            if (data.response.business_model.length === 0) {
                showErrorMessage('Oops! No business model found. Please contact the administrator!');
                return;
            }

            $('#voucher_token').val(data.response.voucher_token);

            var productsHtml = '';
            $.each(data.response.products, function (index, product) {
                if (data.response.business_model.name === 'monthly') {
                    var htmlStyle = '';

                    if (product.background_type && product.background_type === 'image' && product.background) {
                        htmlStyle = 'background-image: url(' + product.background + '?v=' + $.now() + '); background-size:cover;'
                    } else if (product.background_type && product.background_type === 'color' && product.background) {
                        htmlStyle = 'background-color: ' + product.background + ';'
                    } else {
                        htmlStyle = 'background-color: #fff;'
                    }

                    if (product.plan_border) {
                        htmlStyle += 'border: 5px solid ' + product.plan_border + ';'
                    }

                    productsHtml += '<div class="form-check form-check-inline"> ' +
                        '<input class="btn-check" type="radio" name="product" id="product_' + product.product_id + '" value="' + product.product_id + '" onchange="activateGoToPaymentButton()"> ' +
                        '<label class="btn" for="product_' + product.product_id + '" style="' + htmlStyle + '">' + product.name + ' </label> ' +
                        '<span>' + product.price_currency + product.price + ' </span> ' +
                        '</div>';
                } else {
                    productsHtml += '<div class="form-check form-check-inline"> ' +
                        '<input class="btn-check" type="radio" name="product" id="product_' + product.product_id + '" value="' + product.product_id + '" onchange="activateGoToPaymentButton()"> ' +
                        '<label class="btn btn-secondary" for="product_' + product.product_id + '">' + product.name + ' </label> ' +
                        '<span>' + product.price_currency + product.price + ' </span> ' +
                        '</div>';
                }
            });

            if (data.response.business_model.name === 'monthly') {
                productsHtml += '<div class="btn-row">' +
                    '<input class="btn-next" type="button" value="NEXT" id="btn-go-to-payment" onclick="onClickBtnNextToPaymentGateways()" disabled>' +
                    '</div>';
            } else {
                productsHtml += '<div class="btn-row">' +
                    '<input class="btn-prev" type="button" value="BACK" onclick="onClickBtnBackFromVoucherProducts()">' +
                    '<input class="btn-next" type="button" value="NEXT" id="btn-go-to-payment" onclick="onClickBtnNextToPaymentGateways()" disabled>' +
                    '</div>';
                $('#enterVoucherSection').removeClass('d-none');
            }

            $('#products_list').html(productsHtml);

            var paymentGatewaysHtml = '';
            $.each(data.response.gateways, function (index, paymentGateway) {
                paymentGatewaysHtml += '<div class="form-check form-check-inline"> ' +
                    '<input class="btn-check" type="radio" name="payment_gateway" id="payment_gateway_' + paymentGateway.payment_id + '" value="' + paymentGateway.payment_id + '" onchange="activateCheckoutButton()"> ' +
                    '<label class="btn btn-secondary" for="payment_gateway_' + paymentGateway.payment_id + '">' + '<img src=\'' + CHECKOUT_URL + '/templates/arra/img/captiv-portal/payment-gateways/' + paymentGateway.payment_id + '.png\' />' + ' </label> ' +
                    '<span>' + paymentGateway.displayname + ' </span> ' +
                    '</div>';
            });
            $('#payment_gateways').html(paymentGatewaysHtml);

        })
        .always(function() {
            resolve(true);
        });
    })
}

function onClickBtnGetVouchers() {
    debugLog('call function onClickBtnGetVouchers');
    clearErrors();
    $('#enterVoucherSection').addClass('d-none');
    $('#subtitle-get-vouchers').removeClass('d-none');
    $('#products_list').parent().removeClass('d-none');
    $('#promo_vouchers').parent().removeClass('d-none');
}

function onClickBtnBackFromVoucherProducts() {
    debugLog('call function onClickBtnBackFromVoucherProducts');
    clearErrors();
    $('#subtitle-get-vouchers').addClass('d-none');
    $('#products_list').parent().addClass('d-none');
    $('#promo_vouchers').parent().addClass('d-none');
    $('#enterVoucherSection').removeClass('d-none');
}

function onClickBtnNextToPaymentGateways() {
    debugLog('call function onClickBtnNextToPaymentGateways');
    clearErrors();
    $('#subtitle-get-vouchers').addClass('d-none');
    $('#subtitle-get-user-subscription').addClass('d-none');
    $('#subtitle-get-node-subscription').addClass('d-none');
    $('#products_list').parent().addClass('d-none');
    $('#promo_vouchers').parent().addClass('d-none');
    $('#promo_user_subscription').parent().addClass('d-none');
    $('#promo_node_subscription').parent().addClass('d-none');
    $('#payment_gateways').parent().removeClass('d-none');
}

function onClickBtnBackToProducts() {
    debugLog('call function onClickBtnBackToProducts');
    clearErrors();
    if (BUSINESS_MODEL_NAME === 'hotspot') {
        $('#subtitle-get-vouchers').removeClass('d-none');
        $('#promo_vouchers').parent().removeClass('d-none');
    } else if (BUSINESS_MODEL_NAME === 'monthly') {
        if (BUSINESS_MODEL_TYPE === 'user') {
            $('#subtitle-get-user-subscription').removeClass('d-none');
            $('#promo_user_subscription').parent().removeClass('d-none');
            $('#enterUserSubscriptionSection').addClass('d-none');
        }
        if (BUSINESS_MODEL_TYPE === 'router') {
            $('#subtitle-get-node-subscription').removeClass('d-none');
            $('#promo_node_subscription').parent().removeClass('d-none');
        }
    } else {
        window.location.reload();
    }

    $('#products_list').parent().removeClass('d-none');
    $('#payment_gateways').parent().addClass('d-none');
}

function checkout() {
    debugLog('call function checkout');
    if (preview_mode) return;
    showLoading();
    $('#btn-checkout').prop('disabled', true);

    if (!checkValidParamsInUrl()) {
        $('#btn-checkout').prop('disabled', false);
        hideLoading();
        return;
    }

    let productId = $("input[name='product']:checked").val();
    let paymentGateway = $("input[name='payment_gateway']:checked").val();
    let clientMac = urlParams.get('client_mac');
    let routerMac = urlParams.get('mac');

    if (!productId) {
        $('#btn-checkout').prop('disabled', false);
        hideLoading();
        showErrorMessage('Selected product is not valid! Please try again!');
        return;
    }
    if (!paymentGateway) {
        $('#btn-checkout').prop('disabled', false);
        hideLoading();
        showErrorMessage('Selected payment method is not valid! Please try again!');
        return;
    }

    let voucherToken = getVoucherTokenFromLocalstorage() || '';
    if (voucherToken.length === 0) {
        setVoucherTokenInLocalstorage();
        voucherToken = getVoucherTokenFromLocalstorage() || '';
        if (voucherToken.length === 0) {
            $('#btn-checkout').prop('disabled', false);
            showErrorMessage('Something went wrong when get voucher token. Please try again later!');
            hideLoading();
            return;
        }
    }

    let currentUrl = window.location.href;
    let tempUrl = currentUrl.split('&voucher=');
    if (tempUrl.length > 1) {
        currentUrl = tempUrl[0];
    }
    tempUrl = currentUrl.split('&subkey=');
    if (tempUrl.length > 1) {
        currentUrl = tempUrl[0];
    }
    tempUrl = currentUrl.split('&router_login=');
    if (tempUrl.length > 1) {
        currentUrl = tempUrl[0];
    }
    let redirectUrl = CHECKOUT_URL + '/index.php?m=arranetworkscore&action=buy_voucher' +
        '&pid=' + productId +
        '&payment=' + paymentGateway +
        '&backurl=' + encodeURIComponent(currentUrl) +
        '&mac_address=' + routerMac +
        '&client_mac=' + clientMac +
        '&vt=' + voucherToken;
    debugLog('Checkout Redirect URL: ', redirectUrl);
    window.location.href = redirectUrl;
}

function showReconnectMessage() {
    //$('#enterVoucherSection').addClass('d-none');
    //$('#enterUserSubscriptionSection').addClass('d-none');
    $('#success_message').addClass('d-none');
    showErrorMessage('Please Reconnect to the network!');
}
function successLogin() {
    debugLog('call function successLogin');
    clearErrors();
    $('#enterVoucherSection').addClass('d-none');
    $('#enterUserSubscriptionSection').addClass('d-none');
    $('#products_list').parent().addClass('d-none');
    $('#success_message').removeClass('d-none');
    window.location.href = $('#redirect_url_after_success_login').val();
}

function successVoucherLogin(voucherCode) {
    debugLog('call function successVoucherLogin');
    clearErrors();
    $('#enterVoucherSection').addClass('d-none');
    $('#enterUserSubscriptionSection').addClass('d-none');
    $('#products_list').parent().addClass('d-none');
    $('#success_message').removeClass('d-none');
    $('.subtitle').addClass('d-none');
    $.ajax({
        url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=mark_device_active&type=voucher&voucher='+voucherCode+'&client_mac='+urlParams.get('client_mac'),
        type: 'GET',
        data: {}
    })
    .always(function (response) {
        window.location.href = $('#redirect_url_after_success_login').val();
    });
}
function activateGoToPaymentButton() {
    $('#btn-go-to-payment').removeAttr('disabled');
}

function activateCheckoutButton() {
    if (!preview_mode) {
        $('#btn-checkout').removeAttr('disabled');
    }
}

function openUserSubscriptionKeySection() {
    debugLog('call function openUserSubscriptionKeySection')
    clearErrors();
    $('#enterVoucherSection').addClass('d-none');
    $('#enterUserSubscriptionSection').removeClass('d-none');
    $('#products_list').parent().addClass('d-none');
    $('#promo_user_subscription').parent().addClass('d-none');
    $('#subtitle-get-user-subscription').addClass('d-none');
}

function autocompleteVoucherInput() {
    debugLog('call function autocompleteVoucherInput');
    return new Promise((resolve, reject) => {
        if (urlParams.has('voucher') && BUSINESS_MODEL_NAME === 'hotspot') {
            $('#txtVoucherCode').val(urlParams.get('voucher'));
            setVoucherInLocalstorage(urlParams.get('voucher'));
            resolve(true);
        } else {
            let lastVoucherCode = getVoucherFromLocalstorage();
            if (lastVoucherCode && lastVoucherCode !== '') {
                $('#txtVoucherCode').val(lastVoucherCode);
            }
            resolve(true);
        }
    });
}

function submitUserSubscriptionKey() {
    debugLog('call function submitUserSubscriptionKey');
    showLoading();
    clearErrors();
    let subscriptionKey = $('#subscription_key_input').val();
    if (!subscriptionKey || subscriptionKey === '') {
        showErrorMessage('Please enter subscription key!');
        return;
    }

    let macAddress = urlParams.get('mac');
    let clientMacAddress = urlParams.get('client_mac');

    $.ajax({
        url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=add_user_subsciption_device&device_mac=' + clientMacAddress + '&us=' + subscriptionKey + '&router_mac_address=' + macAddress,
        type: 'GET',
        data: {}
    })
        .done(function (response) {
            if (response.success) {
                $('#txtUsername').val(clientMacAddress);
                $('#txtPassword').val(clientMacAddress);
                if (response.redirect_url && response.redirect_url.length > 0) {
                    $('#redirect_url_after_success_login').val(response.redirect_url);
                }
                $('#btnConnectWithSubscription').trigger('click');
            } else {
                showErrorMessage(response.message);
            }
        })
        .fail(function (error) {
            debugLog('Fail submitUserSubscriptionKey: ', error)
            if (error.status === 422) {
                showErrorMessage(error.responseJSON.message || 'Something went wrong! Please try again later!');
            } else {
                showErrorMessage('Something went wrong! Please try again later!');
            }
        })
        .always(function() {
            hideLoading();
        });
}

function checkSubscriptionKeyInURL() {
    debugLog('call function checkSubscriptionKeyInURL');
    return new Promise((resolve, reject) => {
        if (urlParams.has('subkey') && BUSINESS_MODEL_NAME === 'monthly' && BUSINESS_MODEL_TYPE === 'user') {
            debugLog('Found subkey in URL: '+urlParams.get('subkey'));
            $('#products_list').parent().addClass('d-none');
            let subscriptionKey = urlParams.get('subkey');
            let clientMacAddress = urlParams.get('client_mac');
            $('#subscription_key_input').val(subscriptionKey);
            $.ajax({
                url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=check_user_subscription&device_mac=' + clientMacAddress + '&us=' + subscriptionKey,
                type: 'GET',
                data: {}
            })
            .done(function (response) {
                if (response.success) {
                    $('#txtUsername').val(clientMacAddress);
                    $('#txtPassword').val(clientMacAddress);
                    $('#btnConnectWithSubscription').trigger('click');
                } else {
                    showErrorMessage(response.message);
                    resolve(true);
                    //openUserSubscriptionKeySection();
                }
            })
            .fail(function (error) {
                debugLog('Fail Ajax /index.php?m=arranetworkscore&action=check_user_subscription&device_mac=' + clientMacAddress + '&us=' + subscriptionKey+': ', error)
                if (error.status === 422) {
                    showErrorMessage(error.responseJSON.message || 'Something went wrong! Please try again later!');
                } else {
                    showErrorMessage('Something went wrong! Please try again later!');
                }
                resolve(true);
            })
        } else {
            resolve(true);
        }
    });

}

function checkRouterLoginInURL() {
    debugLog('call function checkRouterLoginInURL');
    return new Promise((resolve, reject) => {
        if (urlParams.has('router_login') && BUSINESS_MODEL_NAME === 'monthly' && BUSINESS_MODEL_TYPE === 'router') {
            let nodeMacAddress = urlParams.get('mac');
            $.ajax({
                url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=check_node_subscription&node_mac=' + nodeMacAddress ,
                type: 'GET',
                data: {}
            })
            .done(function (response) {
                if (response.success) {
                    $('#txtUsername').val(nodeMacAddress);
                    $('#txtPassword').val('macauth');
                    $('#btnConnectWithNode').trigger('click');
                } else {
                    showErrorMessage(response.message);
                    resolve(true);
                }
            })
            .fail(function (error) {
                debugLog('Fail Ajax /index.php?m=arranetworkscore&action=check_node_subscription&node_mac=' + nodeMacAddress +': ', error)
                if (error.status === 422) {
                    showErrorMessage(error.responseJSON.message || 'Something went wrong! Please try again later!');
                } else {
                    showErrorMessage('Something went wrong! Please try again later!');
                }
                resolve(true);
            })
        } else {
            resolve(true);
        }
    })
}

function successSubscriptionLogin() {
    debugLog('call function successSubscriptionLogin')
    showLoading();
    clearErrors();
    $('#enterVoucherSection').addClass('d-none');
    $('#enterUserSubscriptionSection').addClass('d-none');
    $('#promo_user_subscription').parent().addClass('d-none');
    $('#products_list').parent().addClass('d-none');
    $('.subtitle').addClass('d-none');

    if (urlParams.has('subkey')) {
        $('#user_subscription_key').html(urlParams.get('subkey'));
        $.ajax({
            url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=mark_device_active&type=subscription&subkey='+urlParams.get('subkey')+'&client_mac='+urlParams.get('client_mac'),
            type: 'GET',
            data: {}
        });
    } else {
        let subscriptionKey = $('#subscription_key_input').val();
        if (subscriptionKey && subscriptionKey.length > 0) {
            $('#user_subscription_key').html(subscriptionKey);
        }
    }

    $('#linkSuccessSubsMyAccount').attr('href', CHECKOUT_URL + '/index.php?m=arranetworkscore&action=gotoclientarea');

    $.ajax({
        url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=getredirecturl' ,
        type: 'GET',
        data: {}
    })
    .done(function (response) {
        if (response.redirect_url) {
            $('#linkSuccessSubsStartBrowsing').attr('href', response.redirect_url);
        }
    });

    $('#successUserSubscription').removeClass('d-none');
    hideLoading();
}

function successNodeLogin() {
    debugLog('call function successNodeLogin')
    showLoading();
    clearErrors();
    $('#enterVoucherSection').addClass('d-none');
    $('#enterUserSubscriptionSection').addClass('d-none');
    $('#products_list').parent().addClass('d-none');
    $('.subtitle').addClass('d-none');

    $.ajax({
        url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=mark_device_active&type=router&mac='+urlParams.get('mac') ,
        type: 'GET',
        data: {}
    });

    $('#linkSuccessNodeMyAccount').attr('href', CHECKOUT_URL + '/index.php?m=arranetworkscore&action=gotoclientarea');

    $.ajax({
        url: CHECKOUT_URL + '/index.php?m=arranetworkscore&action=getredirecturl' ,
        type: 'GET',
        data: {}
    })
    .done(function (response) {
        if (response.redirect_url) {
            $('#linkSuccessNodeStartBrowsing').attr('href', response.redirect_url);
        }
    });

    $('#successNodeSubscription').removeClass('d-none');
    hideLoading();
}


