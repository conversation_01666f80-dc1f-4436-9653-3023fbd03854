html, body {
}

.bg_theme {
  padding-top: 0px;
  padding-bottom: 0px;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  font-family: 'Muli', sans-serif;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background-repeat: no-repeat;
}


.bg_theme_arra {
	background: #3c3d43 url("../img/background-light-arra.png") no-repeat bottom center;
	width: 100% !important;
	height: 100% !important;
}
.no-internet {
	width: 100%;
	display: flex;
	align-content: center;
	justify-content: center;
	flex-direction: column;
	text-align: center;
	padding: 0;
	height: 100%;
}

.no-internet h2 {
	font-size: 24px;
	font-family: Gotham, "Helvetica Neue", Helvetica, Aria<PERSON>, "sans-serif";
	color: #3F4148;
	margin: 20px 0 10px 0;
	padding: 0;
}

.no-internet p {
	font-size: 16px;
	font-family: Gotham, "Helvetica Neue", Helvetica, Arial, "sans-serif";
	color: rgba(123,125,147,1.00);
	margin: 0;
	padding: 0;
	line-height: 1.3;
}
.refresh-bt{
	margin-top: 20px;
}

.refresh-bt .payment-back {
	margin: 0;
}

#products_list, #payment_gateways {
	display:flex;
	flex-wrap: wrap;
	justify-content: center;
}

#payment_gateways {
	margin-bottom: 20px;
}

#products_list .form-check, #payment_gateways .form-check {
	padding: 0;
	margin: 0 10px;
	flex: 1;
	max-width: 300px;
	position: relative;
}

#products_list .form-check > span, #payment_gateways .form-check > span  {
	position: absolute;
	bottom: 30px;
	display: block;
	text-align: center;
	left: 0;
	width: 100%;
	font-size: 21px;
	font-weight: 700;
	pointer-events: none;
	transition: all 0.2s ease-in-out;
}

#products_list .form-check > .btn, #payment_gateways .form-check > .btn {
	padding: 30px 30px 80px 30px;
	font-size: 28px;
    font-weight: 700;
    text-shadow: 0 0 13px rgba(0,0,0,0.2);
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.3);
	border-radius: 12px;
	width: 100%;
	height: 100%;
	transition: all 0.2s ease-in-out;
}

#payment_gateways .form-check > .btn {
	background: #fff;
	border: 5px solid #000;
}

#products_list .btn-row, #payment_gateways .btn-row {
	width: 100%;
	padding: 20px 0 10px 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

#products_list input[type="radio"]:checked+.btn, #products_list .form-check > .btn:hover, #payment_gateways input[type="radio"]:checked+.btn, #payment_gateways .form-check > .btn:hover  {
	border-color: #2991F7 !important;
}

.btn-next, #products button {
	display: inline-block;
	padding: 10px 20px;
	color: #fff;
	background: #006ad1;
	font-size:18px;
	font-weight: 700;
	text-transform: uppercase;
	margin: 15px 0;
	border-radius: 6px;
	transition: all 0.3s ease-in-out;
	text-decoration: none;
	min-width: 150px;
	border: none;
	outline: none;
	cursor: pointer;
}

.btn-next:hover,  #products button:hover{
	background: #0054a6;
}

.btn-next:disabled, .btn-next[disabled] , #products button:disabled,  #products button[disabled]{
  background: #ccc;
  cursor: default;
}

#products button:first-child {
	background: #000;
	margin-right: 15px;
}

#products button:first-child:hover {
	background: #0054a6;
}

.btn-prev {
	display: inline-block;
	padding: 10px 20px;
	color: #fff;
	background: #000000;
	font-size: 18px;
	font-weight: 700;
	text-transform: uppercase;
	margin: 15px 0;
	border-radius: 6px;
	transition: all 0.3s ease-in-out;
	text-decoration: none;
	min-width: 150px;
	border: none;
	outline: none;
	cursor: pointer;
	margin-right: 10px;
}


.btn-prev:hover {
	background: #0054a6;
}

#promo_user_subscription p {
	margin-left: 10px;
	text-align: center;
	font-size:16px;
	max-width: 380px;
	padding: 15px;
	color: #fff;
	border: 1px solid #fff;
	border-radius: 8px;
	margin: 0 auto;
}

#promo_user_subscription a {
	color: #fff;
}

.sub-success-head > h2 {
	text-align: center;
	font-size: 32px;
	color: #fff;
	margin: 10px 0 20px 0;
}

.sub-success-head > p {
	font-size: 16px;
	color: #fff;
	padding: 10px;
	max-width: 480px;
	border-radius: 8px;
	border: 1px solid rgba(255,255,255,0.5);
	margin: 0 auto;
}

.sub-success-head > p span {
	display: block;
	font-size: 24px;
}

.account-links {
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 15px 0;
}

.account-links > a {
	font-size: 16px;
	background: rgba(255,255,255,0.1);
	border: 1px solid rgba(255,255,255,0.5);
	padding: 5px 10px;
	border-radius: 8px;
	transition: all 0.2s ease-in-out;
	margin: 0 10px;
	text-decoration: none;
	display: inline-flex;
	justify-content: center;
	align-items: center;
}

.account-links > a svg {
	width: 18px;
	height: 18px;
	margin-right: 5px;
}

.account-links > a svg path {
	fill:#fff;
}

.account-links > a:hover {
	background: rgba(255,255,255,0.2);
	border: 1px solid rgba(255,255,255,0.5);
}


.media-devices-wrap {
	padding: 30px 0 10px 0;
}

.media-devices-wrap {
	color: #fff;
}

.media-devices-wrap > h2 {
	text-align: center;
	font-size: 32px;
	color: #fff;
	margin: 10px 0 20px 0;
	padding: 0;
}

.media-devices-wrap > h3 {
	text-align: center;
	font-size: 16px;
	font-weight: 400;
	color: #fff;
	margin: 0px 0 20px 0;
}


.media-devices {
	display: flex;
	justify-content: center;
}

.media-devices-col {
	margin: 0 15px;
	border: 1px solid rgba(255,255,255,0.2);
	border-radius: 8px;
	text-align: center;
	width: 33%;
	max-width: 320px;
}

.media-devices-col > h2 {
	text-align: center;
	font-size: 18px;
	color: #000;
	margin: 0px 0 0px 0;
	padding: 12px 20px; 
 background: rgb(255,255,255);
background: -moz-linear-gradient(180deg, rgba(255,255,255,1) 10%, rgba(255,255,255,0.***************) 90%);
background: -webkit-linear-gradient(180deg, rgba(255,255,255,1) 10%, rgba(255,255,255,0.***************) 90%);
background: linear-gradient(180deg, rgba(255,255,255,1) 10%, rgba(255,255,255,0.***************) 90%);
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#ffffff",GradientType=1); 
	border-radius: 8px 8px 0 0;
}

.media-devices-col img {
	max-width: 100%;
	height: auto;
}

.media-devices-col .media-devices-content {
	padding: 20px;
}

.media-pt-qr svg {
	width: 160px;
	height: auto;
}

.media-pt-btns {
	display: flex;
	justify-content: center;
	align-items: center;
}

.app-btn {
  padding: 8px 16px;
  max-width: 200px;
  color: #000;
  margin: 20px 10px;
  text-align: left;
  border-radius: 5px;
  text-decoration: none;
  font-family: "Lucida Grande", sans-serif;
  font-size: 10px;
  text-transform: uppercase;
	background-color: #fff;
	transition: all 0.25s linear;
	display: flex;
	justify-content: center;
	align-items: center;
	line-height: 17px;
	
}

.app-btn:hover {
  background-color: #2a53a3;
	color: #fff;
}

.app-btn svg {
  width: 24px;
	height: 24px;
	display: inline-flex;
  margin-right: 7px;
	transition: all 0.25s linear;
}

.app-btn svg path {
	fill:#000;
}

.app-btn:hover svg path {
	fill:#fff;
}

.app-btn .big-txt {
  font-size: 17px;
  text-transform: capitalize;
}

.web-portal-link {
font-size: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
    padding: 5px 10px;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
    margin: 0 10px;
    text-decoration: none;
    display: inline-flex;
    justify-content: center;
    align-items: center;
	color: #fff;
}

.web-portal-link:hover {
	background: rgba(255,255,255,0.2);
	border: 1px solid rgba(255,255,255,0.5);
	color: #fff;
}

.btv-img {
	margin: 20px 0;
}

.btv-img img {
	max-height: 18px;
	width: auto;
}