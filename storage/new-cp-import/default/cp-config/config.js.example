const RADIUSDESK_URL = 'https://radius.dev.arranetworks.com';
const NMS_URL = 'https://nms2.dev.arranetworks.com';
const NMS_GET_GROUP_CONFIG_VERSION_ENDPOINT = '/arra/group-settings/get-group-config-version';
const NMS_GET_GROUP_SETTINGS_ENDPOINT = '/arra/group-settings/get-group-settings-for-cp';


// ########## DO NOT MODIFY THE CODE BELOW ##########
const urlParams = new URLSearchParams(window.location.search);
var devMode = getDevModeFromLocalstorage();
var checkNodeHashVar = true;
var preview_mode = false;
if (urlParams.has('skip_router_hash_check')) {
    checkNodeHashVar = false;
}
if (urlParams.has('preview_mode')) {
    preview_mode = true;
    checkNodeHashVar = false;
}
if (urlParams.has('devmode')) {
    setDevModeInLocalstorage(urlParams.get('devmode'));
    devMode = getDevModeFromLocalstorage();
}
