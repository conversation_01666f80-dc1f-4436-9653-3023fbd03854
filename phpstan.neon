includes:
    - phpstan-baseline.neon
    - tests/phpstan/ignore-by-php-version.neon.php
    - vendor/larastan/larastan/extension.neon
    - vendor/phpstan/phpstan-mockery/extension.neon

parameters:
    phpVersion:
        min: 80200
        max: 80499

    paths:
        - app
        - bootstrap
        - config
        - database
        - LibreNMS
        - resources
        - routes
        - tests

    excludePaths:
        - tests/phpstan/
        - tests/Unit/Data/KafkaDBStoreTest.php
        - LibreNMS/Data/Store/Kafka.php

    scanDirectories:
        - includes

    level: 5

    treatPhpDocTypesAsCertain: false

    checkFunctionNameCase: true
    checkInternalClassCaseSensitivity: true

    checkModelProperties: true

    ignoreErrors:
        - '#Unsafe usage of new static#'

    parallel:
        processTimeout: 300.0


